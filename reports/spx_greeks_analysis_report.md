# SPX Options Greeks Analysis Report

**Generated:** 2025-07-08 21:10:36

**Analysis Period:** 2025-07-01 to 2025-07-07

**Total Days Analyzed:** 4

---

## Executive Summary

This analysis examines SPX options Greeks patterns focusing on forward-looking expiry dates and convergence events that indicate institutional positioning patterns.

**Key Findings:**
Our analysis identified **8 forward convergence events** where multiple Greeks (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>) reach extreme levels simultaneously at future expiry dates. These events suggest significant institutional positioning and potential market volatility around those dates.

The **Vomma-Price correlation of 0.015** indicates weak correlation between volatility risk and price movements. With **0 distribution signals** detected, the analysis reveals patterns of institutional flow that may precede significant price movements.

**Actionable Insights:**
Traders should monitor the identified convergence dates for potential volatility expansion. The forward-looking analysis provides early warning signals for institutional positioning changes.

### Key Metrics:
- **Total Convergence Events:** 8
- **Distribution Signals:** 0
- **OI Collapse Events (Institutional Unwinding):** 0
- **Extreme Greek Days:** 0

## Key Findings

### 1. Convergence Events
Identified 8 convergence events where multiple Greeks reached extreme levels simultaneously:

- **2025-07-18**: Historic Forward Convergence (Score: 12, SPX: 6198.01)
- **2025-07-31**: Major Forward Convergence (Score: 9, SPX: 6198.01)
- **2025-08-15**: Major Forward Convergence (Score: 10, SPX: 6198.01)
- **2025-08-29**: Major Forward Convergence (Score: 9, SPX: 6198.01)
- **2025-09-19**: Major Forward Convergence (Score: 10, SPX: 6198.01)
- **2025-09-30**: Major Forward Convergence (Score: 10, SPX: 6198.01)
- **2025-10-17**: Major Forward Convergence (Score: 10, SPX: 6198.01)
- **2025-12-31**: Forward Convergence (Score: 6, SPX: 6198.01)

### 2. Vomma/Price Divergence Patterns
- Distribution signals detected: 0
- Acceleration signals detected: 1
- Vomma-Price correlation: 0.015

### 3. Market Regime Classification
**Volatility Regimes:**
- Normal: 2 days
- High Vol: 1 days
- Low Vol: 1 days

**Greek Regimes:**
- Extreme: 2 days
- Normal: 1 days
- Collapse: 1 days

## Convergence Events Analysis

Convergence events occur when multiple Greeks reach extreme levels simultaneously, often indicating institutional positioning or market engineering. This analysis uses data-driven thresholds based on statistical percentiles rather than arbitrary values.

### Data-Driven Scoring System:
- **Extreme Vomma (>0.0M):** +3 points
- **Extreme Vanna (>0.0K):** +3 points
- **Extreme Charm:** +2 points
- **Extreme Theta:** +2 points
- **High Open Interest:** +1 point
- **Extreme Delta P/C Ratio:** +1 point

### Event Classifications:
- **Convergence (Score 6-8):** Multiple extremes present
- **Major Convergence (Score 9-11):** Significant institutional activity
- **Historic Convergence (Score 12+):** Unprecedented positioning

### Market Analysis:

The 8 forward convergence events identified represent periods where multiple Greeks reach extreme levels simultaneously at future expiry dates. These patterns typically indicate:

- **Institutional Positioning**: Large-scale options strategies being deployed
- **Volatility Preparation**: Market makers hedging for expected volatility
- **Price Inflection Points**: Potential areas of significant price movement

The convergence events span from July to October 2025, suggesting sustained institutional activity across multiple expiry cycles.

### Detailed Event Analysis:

#### 2025-07-18 - Historic Forward Convergence
- **Extreme Score:** 12
- **SPX Price:** 6198.01
- **Vomma (Total):** 11363542
- **Vanna (Total):** -18
- **Open Interest (Total):** 8696613
- **Strike Range:** 200 - 12000
- **Strike Count:** 7824
- **Days to Expiry:** 17

#### 2025-07-31 - Major Forward Convergence
- **Extreme Score:** 9
- **SPX Price:** 6198.01
- **Vomma (Total):** 6661472
- **Vanna (Total):** -6
- **Open Interest (Total):** 1401344
- **Strike Range:** 1400 - 8400
- **Strike Count:** 3568
- **Days to Expiry:** 30

#### 2025-08-15 - Major Forward Convergence
- **Extreme Score:** 10
- **SPX Price:** 6198.01
- **Vomma (Total):** 16039067
- **Vanna (Total):** -16
- **Open Interest (Total):** 6010063
- **Strike Range:** 200 - 12000
- **Strike Count:** 7056
- **Days to Expiry:** 45

#### 2025-08-29 - Major Forward Convergence
- **Extreme Score:** 9
- **SPX Price:** 6198.01
- **Vomma (Total):** 7751078
- **Vanna (Total):** -8
- **Open Interest (Total):** 723472
- **Strike Range:** 1200 - 8200
- **Strike Count:** 3192
- **Days to Expiry:** 59

#### 2025-09-19 - Major Forward Convergence
- **Extreme Score:** 10
- **SPX Price:** 6198.01
- **Vomma (Total):** 14927691
- **Vanna (Total):** -13
- **Open Interest (Total):** 12961550
- **Strike Range:** 200 - 12000
- **Strike Count:** 5896
- **Days to Expiry:** 80

#### 2025-09-30 - Major Forward Convergence
- **Extreme Score:** 10
- **SPX Price:** 6198.01
- **Vomma (Total):** 10694419
- **Vanna (Total):** -7
- **Open Interest (Total):** 1998150
- **Strike Range:** 1200 - 8000
- **Strike Count:** 3776
- **Days to Expiry:** 91

#### 2025-10-17 - Major Forward Convergence
- **Extreme Score:** 10
- **SPX Price:** 6198.01
- **Vomma (Total):** 14183000
- **Vanna (Total):** -6
- **Open Interest (Total):** 3097250
- **Strike Range:** 200 - 12000
- **Strike Count:** 4808
- **Days to Expiry:** 108

#### 2025-12-31 - Forward Convergence
- **Extreme Score:** 6
- **SPX Price:** 6198.01
- **Vomma (Total):** 4811901
- **Vanna (Total):** -2
- **Open Interest (Total):** 981830
- **Strike Range:** 1200 - 8200
- **Strike Count:** 2422
- **Days to Expiry:** 183

## Vomma/Price Divergence Analysis

Vomma (volatility of volatility) divergence from price movements can signal distribution or acceleration patterns:

### Distribution Pattern:
- Price rises while Vomma explodes higher
- Indicates institutional distribution into retail buying
- Creates perfect trap for late buyers

### Acceleration Pattern:  
- Price falls with negative Vomma
- Creates self-reinforcing decline
- Amplifies volatility impact

### Analysis Results:
- **Vomma-Price Correlation:** 0.015
- **Distribution Signals:** 0
- **Acceleration Signals:** 1

## Market Regime Analysis

Market regimes are classified based on volatility levels and Greek extremes using data-driven thresholds:

### Volatility Regimes:
- **Low Vol:** Bottom 20% of Vega readings
- **Normal:** Middle 60% of Vega readings
- **High Vol:** Top 20% of Vega readings

### Greek Regimes:
- **Normal:** Standard Greek levels
- **Extreme:** Multiple Greeks at extreme levels (85th percentile)
- **Distribution:** Price up + high Greeks (75th percentile)
- **Collapse:** Low Vomma + price down (25th percentile)

### Regime Distribution:

**Volatility Regimes:**
- Normal: 2 days (50.0%)
- High Vol: 1 days (25.0%)
- Low Vol: 1 days (25.0%)

**Greek Regimes:**
- Extreme: 2 days (50.0%)
- Normal: 1 days (25.0%)
- Collapse: 1 days (25.0%)

### Market Interpretation:

The current SPX options market regime distributions suggest that the market is experiencing a mix of normal and extreme conditions. For the volatility regimes, 'normal' has a frequency of 2 while both 'high_vol' and 'low_vol' are at 1. This indicates that the market is primarily in a state of normal volatility with instances of both high and low volatility. Similarly, for the Greek regimes, 'extreme' is prevalent with a frequency of 2, while 'normal' and 'collapse' are at a frequency of 1. This suggests that the market is facing periods of significant Greek value fluctuations, which may be indicative of uncertainty and potentially increased risk-taking behavior.

Extreme Greek regimes often reflect significant changes in institutional positioning and can lead to substantial price action. Institutional investors typically have large portfolios that are sensitive to changes in Greek values. As such, an extreme Greek regime may signal that institutions are rebalancing their positions, leading to increased buying or selling pressure in the marketplace. This can result in substantial price swings and increased volatility. Moving forward, the sustainability of the current regimes will largely be determined by market participants' sentiment and risk tolerance. If institutions continue aggressive positioning adjustments, we may continue to see extreme Greek regimes. However, if there is a shift towards more conservative risk management or if uncertainty subsides, we may see a transition back to 'normal' regimes. Traders should monitor these regime transitions closely as they can offer valuable insights into potential market trends and trading opportunities.

## Charts and Visualizations

The following charts provide visual analysis of the Greeks patterns and market behavior:

### SPX Price and Options Greeks Time Series (Trading Dates)

![SPX Price and Options Greeks Time Series (Trading Dates)](spx_price_and_greeks_chart.png)

### Convergence Events Analysis

![Convergence Events Analysis](spx_convergence_analysis_chart.png)

### Greeks and Price Correlation Matrix

![Greeks and Price Correlation Matrix](spx_correlation_heatmap.png)

### Market Regime Classification

![Market Regime Classification](spx_regime_analysis_chart.png)

### Future Price Projection: SPX Greeks by Expiry Date

![Future Price Projection: SPX Greeks by Expiry Date](spx_expiry_focused_analysis_chart.png)

### Options Expiry Date Distribution

![Options Expiry Date Distribution](spx_expiry_distribution_chart.png)

## Chart Types: Historical vs Future Projection

**Two Analysis Perspectives:**

### 1. Historical Trading Analysis (Time Series Charts)
- **X-Axis**: Trading dates when options data was recorded (April-June 2025)
- **Purpose**: Shows how Greeks evolved during the trading period
- **Use Case**: Understanding past market behavior and patterns

### 2. Future Price Projection Analysis (Expiry-Focused Charts)
- **X-Axis**: Option expiry dates (future dates beyond analysis period)
- **Purpose**: Projects potential price impact based on option expirations
- **Use Case**: Forecasting future price movements and volatility events

**Key Insight**: The "Future Price Projection" chart groups all options by their expiry dates to show when Greeks exposure will impact the market, regardless of when the options were traded.

**Filtering Applied**: Only options with expiry dates AFTER the analysis date are included (no expired options).

## Technical Details

### Data Processing:
- **Source:** SPX options chain data Q2 2025
- **Greeks Calculated:** Delta, Gamma, Vega, Theta, Rho
- **Derived Greeks:** Vomma, Vanna, Charm
- **Aggregation:** Daily totals weighted by Open Interest

### Methodology:
- **Vomma Calculation:** Second derivative of option value with respect to volatility (Vega × d1 × d2 / σ)
- **Vanna Calculation:** Cross-sensitivity between Delta and volatility (-e^(-qT) × φ(d1) × d2 / σ)
- **Charm Calculation:** Rate of change of Delta with respect to time (complex Black-Scholes time decay formula)
- **All Greeks:** Calculated using precise Black-Scholes mathematical formulas, not approximations
- **Extreme Thresholds:** Based on statistical analysis of calculated Greeks data

### Black-Scholes Implementation Details:
**Primary Greeks:**
- **Delta:** ∂V/∂S (price sensitivity)
- **Gamma:** ∂²V/∂S² (Delta sensitivity)
- **Vega:** ∂V/∂σ (volatility sensitivity)
- **Theta:** ∂V/∂t (time decay)
- **Rho:** ∂V/∂r (interest rate sensitivity)

**Advanced Greeks (Second & Cross Derivatives):**
- **Vomma:** ∂²V/∂σ² = Vega × d1 × d2 / σ (volatility convexity)
- **Vanna:** ∂²V/∂S∂σ = -e^(-qT) × φ(d1) × d2 / σ (price-volatility cross-sensitivity)
- **Charm:** ∂²V/∂S∂t (Delta time decay with complex Black-Scholes time components)

**Parameters Used:**
- Risk-free rate: 5.0%
- Dividend yield: 1.5% (SPX)
- Volatility: Implied volatility from options data
- Time to expiration: Calculated from expiry dates

### Risk Considerations:
- Analysis based on historical data patterns
- Market conditions can change rapidly
- Greeks calculations are approximations
- Past patterns may not predict future behavior

---

*This report was generated using the {self.config.TICKER} Greeks Analysis Engine*
