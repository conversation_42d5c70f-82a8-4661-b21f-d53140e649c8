#!/usr/bin/env python3.11
"""
SPX Options Greeks Analysis - Main Execution Script

This script runs the complete SPX options Greeks analysis pipeline:
1. Loads and processes options data
2. Calculates derived Greeks (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)
3. Runs pattern recognition and extreme event analysis
4. Generates comprehensive PDF report with charts

Usage:
    python3.11 run_analysis.py [data_file]

If no data_file is specified, uses the default data/spx_complete_2025_q2.csv
"""

import sys
import os
import argparse
from datetime import datetime
from pathlib import Path

# Add src and config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))

from config import Config
from data_loader import SPXDataLoader
from analytics_engine import GreeksAnalyticsEngine
from report_generator import GreeksReportGenerator

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='SPX Options Greeks Analysis')
    parser.add_argument('--data-file', '-d', type=str, 
                       help='Path to SPX options data CSV file')
    parser.add_argument('--output-dir', '-o', type=str,
                       help='Output directory for reports (default: reports/)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose output')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("SPX OPTIONS GREEKS ANALYSIS")
    print("=" * 60)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Initialize configuration
        config = Config()
        config.ensure_directories()
        
        if args.verbose:
            print("✓ Configuration initialized")
            print(f"  Project root: {config.PROJECT_ROOT}")
            print(f"  Data directory: {config.DATA_DIR}")
            print(f"  Reports directory: {config.REPORTS_DIR}")
            print()
        
        # Step 1: Load and process data
        print("STEP 1: Loading and Processing Data")
        print("-" * 40)
        
        loader = SPXDataLoader(config)
        
        # Use specified data file or default
        data_file = args.data_file if args.data_file else None
        
        print("Loading options data...")
        raw_data = loader.load_data(data_file)
        
        print("Cleaning and validating data...")
        cleaned_data = loader.clean_data()
        
        print("Calculating derived Greeks (Vomma, Vanna, Charm)...")
        processed_data = loader.calculate_derived_greeks()
        
        print("Aggregating daily data...")
        daily_data = loader.aggregate_daily_data()
        
        print("Detecting extreme events...")
        extreme_events = loader.get_extreme_events()
        
        # Print data summary
        summary = loader.get_data_summary()
        print(f"✓ Data processing complete:")
        print(f"  Total days: {summary['total_days']}")
        print(f"  Date range: {summary['date_range']['start']} to {summary['date_range']['end']}")
        print(f"  SPX range: ${summary['spx_price_range']['min']:.2f} - ${summary['spx_price_range']['max']:.2f}")
        print(f"  Max Vomma: {summary['greeks_summary']['max_vomma_m']:.1f}M")
        print(f"  Max Vanna: {summary['greeks_summary']['max_vanna_k']:.1f}K")
        print()
        
        # Step 2: Run Analytics
        print("STEP 2: Running Greeks Analytics")
        print("-" * 40)
        
        engine = GreeksAnalyticsEngine(config)
        engine.set_data(daily_data)
        
        print("Running full analysis pipeline...")
        analysis_results = engine.run_full_analysis()
        
        # Print key findings
        key_findings = analysis_results['summary']['key_findings']
        print(f"✓ Analytics complete:")
        print(f"  Convergence events: {key_findings['total_convergence_events']}")
        print(f"  Distribution signals: {key_findings['distribution_signals']}")
        print(f"  Volume collapse events: {key_findings['volume_collapse_events']}")
        print(f"  Extreme Greek days: {key_findings['extreme_greek_days']}")
        print()
        
        # Step 3: Generate Report
        print("STEP 3: Generating Report and Charts")
        print("-" * 40)
        
        report_gen = GreeksReportGenerator(config)
        report_gen.set_data(daily_data, analysis_results)
        
        print("Generating comprehensive PDF report...")
        pdf_report = report_gen.generate_pdf_report()
        
        # Get file size
        if os.path.exists(pdf_report):
            file_size = os.path.getsize(pdf_report) / (1024 * 1024)  # MB
            print(f"✓ Report generated: {os.path.basename(pdf_report)} ({file_size:.1f} MB)")
        else:
            print(f"✓ Report generated: {os.path.basename(pdf_report)}")
        
        # List all generated files
        print("\nGenerated Files:")
        print("-" * 20)
        reports_dir = config.REPORTS_DIR
        total_size = 0
        
        for file in sorted(os.listdir(reports_dir)):
            file_path = os.path.join(reports_dir, file)
            if os.path.isfile(file_path):
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                total_size += size_mb
                print(f"  {file:<35} {size_mb:>6.1f} MB")
        
        print(f"  {'Total:':<35} {total_size:>6.1f} MB")
        print()
        
        # Success message
        print("=" * 60)
        print("✓ ANALYSIS COMPLETE!")
        print(f"✓ Main report: {pdf_report}")
        print(f"✓ All files saved to: {config.REPORTS_DIR}")
        print(f"✓ Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        return pdf_report
        
    except Exception as e:
        print(f"\n✗ ERROR: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

