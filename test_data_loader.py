"""
Test script for SPX Data Loader
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))

from config import Config
from data_loader import SPXDataLoader

def test_data_loader():
    """Test the data loader functionality"""
    print("Testing SPX Data Loader...")
    print("=" * 50)
    
    # Initialize config and data loader
    config = Config()
    config.ensure_directories()
    
    loader = SPXDataLoader(config)
    
    # Test data loading
    print("1. Loading data...")
    try:
        raw_data = loader.load_data()
        print(f"   ✓ Loaded {len(raw_data)} rows")
        print(f"   ✓ Columns: {list(raw_data.columns)}")
        print(f"   ✓ Date range: {raw_data['date'].min()} to {raw_data['date'].max()}")
    except Exception as e:
        print(f"   ✗ Error loading data: {e}")
        return False
    
    # Test data cleaning
    print("\n2. Cleaning data...")
    try:
        cleaned_data = loader.clean_data()
        print(f"   ✓ Cleaned data: {len(cleaned_data)} rows")
        print(f"   ✓ Added DTE and Moneyness columns")
    except Exception as e:
        print(f"   ✗ Error cleaning data: {e}")
        return False
    
    # Test derived Greeks calculation
    print("\n3. Calculating derived Greeks...")
    try:
        processed_data = loader.calculate_derived_greeks()
        print(f"   ✓ Calculated Vomma, Vanna, Charm")
        print(f"   ✓ Added weighted Greeks by OI")
        
        # Show sample of derived Greeks
        sample = processed_data[['date', 'Vomma', 'Vanna', 'Charm']].head()
        print(f"   Sample derived Greeks:")
        print(sample.to_string(index=False))
    except Exception as e:
        print(f"   ✗ Error calculating derived Greeks: {e}")
        return False
    
    # Test daily aggregation
    print("\n4. Aggregating daily data...")
    try:
        daily_data = loader.aggregate_daily_data()
        print(f"   ✓ Aggregated to {len(daily_data)} days")
        print(f"   ✓ Columns: {list(daily_data.columns)}")
        
        # Show sample of daily aggregates
        sample_cols = ['date', 'spx_close', 'Vomma_M', 'Vanna_K', 'Total_OI_K', 'Delta_PC_Ratio']
        sample = daily_data[sample_cols].head()
        print(f"   Sample daily aggregates:")
        print(sample.to_string(index=False))
    except Exception as e:
        print(f"   ✗ Error aggregating daily data: {e}")
        return False
    
    # Test extreme events detection
    print("\n5. Detecting extreme events...")
    try:
        extreme_events = loader.get_extreme_events()
        print(f"   ✓ Found extreme events:")
        for event_type, events in extreme_events.items():
            print(f"     {event_type}: {len(events)} events")
    except Exception as e:
        print(f"   ✗ Error detecting extreme events: {e}")
        return False
    
    # Test data summary
    print("\n6. Generating data summary...")
    try:
        summary = loader.get_data_summary()
        print(f"   ✓ Data summary:")
        print(f"     Total days: {summary['total_days']}")
        print(f"     Date range: {summary['date_range']['start']} to {summary['date_range']['end']}")
        print(f"     SPX range: {summary['spx_price_range']['min']:.2f} to {summary['spx_price_range']['max']:.2f}")
        print(f"     Max Vomma: {summary['greeks_summary']['max_vomma_m']:.2f}M")
        print(f"     Max Vanna: {summary['greeks_summary']['max_vanna_k']:.2f}K")
    except Exception as e:
        print(f"   ✗ Error generating summary: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✓ All data loader tests passed!")
    return True

if __name__ == "__main__":
    success = test_data_loader()
    if not success:
        sys.exit(1)

