"""
Configuration module for SPX Options Greeks Analysis
"""
import os
from pathlib import Path

class Config:
    """Configuration class for SPX Greeks Analysis"""
    
    # Project paths
    PROJECT_ROOT = Path(__file__).parent.parent
    DATA_DIR = PROJECT_ROOT / "data"
    REPORTS_DIR = PROJECT_ROOT / "reports"
    SRC_DIR = PROJECT_ROOT / "src"
    
    # Data file paths
    SPX_DATA_FILE = DATA_DIR / "spx_complete_2025_q2.csv"
    
    # Analysis parameters
    ANALYSIS_PARAMS = {
        'extreme_vomma_threshold': 50_000_000,  # 50M threshold for extreme Vomma
        'extreme_vanna_threshold': 300_000,     # 300K threshold for extreme Vanna
        'extreme_charm_threshold': 1_000_000,   # 1M threshold for extreme Charm
        'extreme_oi_threshold': 1_000_000,      # 1M threshold for extreme Open Interest
        'volume_collapse_threshold': 0.5,       # 50% volume drop threshold
        'convergence_window_days': 5,           # Days to analyze around convergence events
    }
    
    # Greeks calculation parameters
    GREEKS_PARAMS = {
        'risk_free_rate': 0.05,  # 5% risk-free rate
        'dividend_yield': 0.015,  # 1.5% dividend yield for SPX
        'trading_days_per_year': 252,
    }
    
    # Chart styling
    CHART_STYLE = {
        'figsize': (15, 10),
        'dpi': 300,
        'style': 'seaborn-v0_8',
        'color_palette': 'Set2',
        'font_size': 12,
    }
    
    # Report settings
    REPORT_SETTINGS = {
        'title': 'SPX Options Greeks Analysis Report',
        'author': 'Greeks Analysis Engine',
        'output_format': 'PDF',
        'include_charts': True,
        'chart_quality': 'high',
    }
    
    @classmethod
    def ensure_directories(cls):
        """Ensure all required directories exist"""
        cls.DATA_DIR.mkdir(exist_ok=True)
        cls.REPORTS_DIR.mkdir(exist_ok=True)
        cls.SRC_DIR.mkdir(exist_ok=True)
    
    @classmethod
    def get_output_path(cls, filename):
        """Get full path for output file"""
        return cls.REPORTS_DIR / filename
    
    @classmethod
    def get_data_path(cls, filename):
        """Get full path for data file"""
        return cls.DATA_DIR / filename

