"""
Configuration module for SPX Options Greeks Analysis
"""
import re
from pathlib import Path
from typing import Optional, Tuple

class Config:
    """Configuration class for SPX Greeks Analysis"""

    # Ticker symbol (configurable)
    TICKER = "SPX"  # Change this to analyze different tickers

    # Project paths
    PROJECT_ROOT = Path(__file__).parent.parent
    DATA_DIR = PROJECT_ROOT.parent / "optionhistory"
    REPORTS_DIR = PROJECT_ROOT / "reports"
    SRC_DIR = PROJECT_ROOT / "src"

    # Data file paths (dynamically determined)
    SPX_DATA_FILE = None  # Will be set by get_latest_data_file()
    
    # Analysis parameters
    ANALYSIS_PARAMS = {
        # Data filtering parameters
        'filter_future_expiry_only': True,     # Only include options with expiry > today
        'min_dte': 1,                          # Minimum days to expiration (1 = exclude same-day expiry)
        'convergence_window_days': 5,           # Days to analyze around convergence events

        # Legacy thresholds (replaced by data-driven approach)
        'extreme_vomma_threshold': 50_000_000,  # 50M threshold for extreme Vomma (legacy)
        'extreme_vanna_threshold': 300_000,     # 300K threshold for extreme Vanna (legacy)
        'extreme_charm_threshold': 1_000_000,   # 1M threshold for extreme Charm (legacy)
        'extreme_oi_threshold': 1_000_000,      # 1M threshold for extreme Open Interest (legacy)
        'volume_collapse_threshold': 0.5,       # 50% volume drop threshold
    }

    # Data-driven analysis configuration
    DATA_DRIVEN_CONFIG = {
        # Percentile thresholds for extreme event detection
        'extreme_percentile': 0.90,             # 90th percentile for extreme events (top 10%)
        'high_percentile': 0.85,                # 85th percentile for high events
        'medium_high_percentile': 0.75,         # 75th percentile for medium-high events
        'low_percentile': 0.25,                 # 25th percentile for low events
        'very_high_percentile': 0.95,           # 95th percentile for very extreme events
        'ultra_high_percentile': 0.99,          # 99th percentile for ultra extreme events

        # Convergence scoring weights
        'vomma_score_weight': 3,                # Points for extreme Vomma
        'vanna_score_weight': 3,                # Points for extreme Vanna
        'charm_score_weight': 2,                # Points for extreme Charm
        'theta_score_weight': 2,                # Points for extreme Theta
        'oi_score_weight': 1,                   # Points for high Open Interest
        'delta_pc_score_weight': 1,             # Points for extreme Delta P/C ratio

        # Event classification thresholds
        'convergence_min_score': 6,             # Minimum score for convergence event
        'major_convergence_min_score': 9,       # Minimum score for major convergence
        'historic_convergence_min_score': 12,   # Minimum score for historic convergence

        # Regime analysis parameters
        'vol_regime_low_threshold': 0.20,       # Bottom 20% for low vol regime
        'vol_regime_high_threshold': 0.80,      # Top 20% for high vol regime
        'greek_regime_extreme_threshold': 0.85, # 85th percentile for extreme Greek regime
        'greek_regime_distribution_threshold': 0.75, # 75th percentile for distribution regime
        'greek_regime_collapse_threshold': 0.25, # 25th percentile for collapse regime

        # Volume analysis thresholds
        'volume_collapse_threshold': -50,        # Percentage drop for volume collapse
        'volume_spike_threshold': 100,           # Percentage increase for volume spike
        'volume_ma_multiplier': 2,               # Multiplier for volume spike detection
        'volume_ma_short_period': 5,             # Short-term moving average period
        'volume_ma_long_period': 10,             # Long-term moving average period

        # Price change analysis
        'percentage_multiplier': 100,            # Convert decimal to percentage

        # Default implied volatility
        'default_implied_volatility': 0.20,     # 20% default IV when missing
    }

    # Chart and visualization configuration
    CHART_CONFIG = {
        # Chart file names and descriptions
        'chart_files': {
            'price_and_greeks': 'price_and_greeks_chart.png',
            'convergence_analysis': 'convergence_analysis_chart.png',
            'correlation_heatmap': 'correlation_heatmap.png',
            'regime_analysis': 'regime_analysis_chart.png',
            'expiry_focused': 'expiry_focused_analysis_chart.png',
            'expiry_distribution': 'expiry_distribution_chart.png'
        },

        'chart_descriptions': {
            'price_and_greeks_chart.png': 'SPX Price and Options Greeks Time Series (Trading Dates)',
            'convergence_analysis_chart.png': 'Convergence Events Analysis',
            'correlation_heatmap.png': 'Greeks and Price Correlation Matrix',
            'regime_analysis_chart.png': 'Market Regime Classification',
            'expiry_focused_analysis_chart.png': 'Future Price Projection: Greeks by Expiry Date',
            'expiry_distribution_chart.png': 'Options Expiry Date Distribution'
        },

        # Chart styling parameters
        'figure_size_large': (15, 18),          # Large chart dimensions (4 subplots)
        'figure_size_extra_large': (15, 16),    # Extra large for complex charts
        'figure_size_medium': (15, 12),         # Medium chart dimensions (2x2 subplots)
        'figure_size_standard': (15, 10),       # Standard chart dimensions (2 subplots)
        'figure_size_correlation': (12, 10),    # Correlation heatmap dimensions
        'figure_size_small': (10, 6),           # Small chart dimensions
        'dpi': 300,                             # Chart resolution
        'context_lines_before': 5,              # Context lines before matches
        'context_lines_after': 5,               # Context lines after matches

        # Chart styling constants
        'grid_alpha': 0.3,                      # Grid transparency
        'line_alpha': 0.7,                      # Line transparency for overlays
        'scatter_alpha': 0.7,                   # Scatter plot transparency
        'bar_alpha': 0.6,                       # Bar chart transparency
        'marker_size': 4,                       # Default marker size
        'scatter_size': 50,                     # Scatter plot marker size
        'regime_scatter_size': 30,              # Regime analysis scatter size
        'line_width': 2,                        # Default line width
        'vertical_line_width': 3,               # Vertical reference line width
        'rotation_angle': 45,                   # X-axis label rotation
        'font_size_title': 16,                  # Chart title font size
        'font_size_subtitle': 12,               # Chart subtitle font size

        # Date formatting
        'date_format': '%m/%d',                 # Date format for x-axis labels
        'date_interval_weekly': 2,              # Weekly interval for date locator
        'date_interval_daily': 3,               # Daily interval for 30-day charts

        # Future analysis constants
        'future_analysis_days': 30,             # Days ahead for future analysis
        'projection_impact_factors': {
            'vomma_factor': 0.1,                # Vomma impact on price projection
            'vanna_factor': 0.5,                # Vanna impact on price projection
            'charm_factor': -0.2,               # Charm decay factor (negative)
        },

        # Scale conversion factors
        'million_scale': 1_000_000,             # Convert to millions
        'thousand_scale': 1000,                 # Convert to thousands

        # Chart threshold lines and reference values
        'convergence_threshold': 4,              # Convergence threshold line
        'major_convergence_threshold': 6,        # Major convergence threshold line
        'delta_pc_bullish_threshold': 0.5,       # Bullish Delta P/C ratio threshold
        'delta_pc_bearish_threshold': 1.5,       # Bearish Delta P/C ratio threshold

        # Chart colors (for consistency)
        'colors': {
            'primary_blue': 'blue',
            'primary_red': 'red',
            'primary_green': 'green',
            'primary_orange': 'orange',
            'primary_purple': 'purple',
            'primary_brown': 'brown',
            'primary_cyan': 'cyan',
            'primary_black': 'black',
            'dark_blue': 'darkblue',
            'sky_blue': 'skyblue',
            'light_green': 'lightgreen',
        },
    }

    # Report generation configuration
    REPORT_CONFIG = {
        # File names
        'markdown_report': 'spx_greeks_analysis_report.md',
        'pdf_report': 'spx_greeks_analysis_report.pdf',

        # Report sections
        'include_executive_summary': True,
        'include_convergence_analysis': True,
        'include_regime_analysis': True,
        'include_technical_details': True,

        # Narrative generation
        'use_chatgpt_narratives': True,
        'chatgpt_max_tokens': 500,              # Maximum tokens for ChatGPT responses
        'chatgpt_temperature': 0.7,             # ChatGPT creativity level
        'narrative_cache_enabled': True,        # Enable narrative caching
    }
    
    # Greeks calculation parameters
    GREEKS_PARAMS = {
        'risk_free_rate': 0.05,  # 5% risk-free rate
        'dividend_yield': 0.015,  # 1.5% dividend yield for SPX
        'trading_days_per_year': 252,
    }
    
    # Chart styling
    CHART_STYLE = {
        'figsize': (15, 10),
        'dpi': 300,
        'style': 'seaborn-v0_8',
        'color_palette': 'Set2',
        'font_size': 12,
    }
    
    # Report settings
    REPORT_SETTINGS = {
        'title': 'SPX Options Greeks Analysis Report',
        'author': 'Greeks Analysis Engine',
        'output_format': 'PDF',
        'include_charts': True,
        'chart_quality': 'high',
    }
    
    @classmethod
    def ensure_directories(cls):
        """Ensure all required directories exist"""
        cls.DATA_DIR.mkdir(exist_ok=True)
        cls.REPORTS_DIR.mkdir(exist_ok=True)
        cls.SRC_DIR.mkdir(exist_ok=True)
    
    @classmethod
    def get_output_path(cls, filename):
        """Get full path for output file"""
        return cls.REPORTS_DIR / filename
    
    @classmethod
    def get_data_path(cls, filename):
        """Get full path for data file"""
        return cls.DATA_DIR / filename

    @classmethod
    def find_latest_history_directory(cls) -> Optional[Tuple[str, str, str]]:
        """
        Find the most recent history directory in format {year}_{quarter}_option_chain
        Returns tuple of (directory_name, year, quarter) or None if not found
        """
        if not cls.DATA_DIR.exists():
            return None

        pattern = re.compile(r'^(\d{4})_(q[1-4])_option_chain$')
        directories = []

        for item in cls.DATA_DIR.iterdir():
            if item.is_dir():
                match = pattern.match(item.name)
                if match:
                    year, quarter = match.groups()
                    # Convert quarter to numeric for sorting (q1=1, q2=2, etc.)
                    quarter_num = int(quarter[1])
                    directories.append((item.name, int(year), quarter_num, year, quarter))

        if not directories:
            return None

        # Sort by year and quarter (most recent first)
        directories.sort(key=lambda x: (x[1], x[2]), reverse=True)
        latest = directories[0]

        return (latest[0], latest[3], latest[4])  # (directory_name, year_str, quarter_str)

    @classmethod
    def get_latest_data_file(cls) -> Optional[Path]:
        """
        Get the path to the most recent data file
        Format: {ticker}_complete_{year}_{quarter}.csv
        """
        latest_dir_info = cls.find_latest_history_directory()
        if not latest_dir_info:
            return None

        directory_name, year, quarter = latest_dir_info
        filename = f"{cls.TICKER.lower()}_complete_{year}_{quarter}.csv"
        file_path = cls.DATA_DIR / directory_name / filename

        if file_path.exists():
            return file_path
        else:
            return None

    @classmethod
    def initialize_data_file(cls):
        """Initialize the SPX_DATA_FILE with the latest available data file"""
        cls.SPX_DATA_FILE = cls.get_latest_data_file()
        return cls.SPX_DATA_FILE

