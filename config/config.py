"""
Configuration module for SPX Options Greeks Analysis
"""
import os
import re
from pathlib import Path
from typing import Optional, Tuple

class Config:
    """Configuration class for SPX Greeks Analysis"""

    # Ticker symbol (configurable)
    TICKER = "SPX"  # Change this to analyze different tickers

    # Project paths
    PROJECT_ROOT = Path(__file__).parent.parent
    DATA_DIR = PROJECT_ROOT.parent / "optionhistory"
    REPORTS_DIR = PROJECT_ROOT / "reports"
    SRC_DIR = PROJECT_ROOT / "src"

    # Data file paths (dynamically determined)
    SPX_DATA_FILE = None  # Will be set by get_latest_data_file()
    
    # Analysis parameters
    ANALYSIS_PARAMS = {
        'extreme_vomma_threshold': 50_000_000,  # 50M threshold for extreme Vomma
        'extreme_vanna_threshold': 300_000,     # 300K threshold for extreme Vanna
        'extreme_charm_threshold': 1_000_000,   # 1M threshold for extreme Charm
        'extreme_oi_threshold': 1_000_000,      # 1M threshold for extreme Open Interest
        'volume_collapse_threshold': 0.5,       # 50% volume drop threshold
        'convergence_window_days': 5,           # Days to analyze around convergence events
    }
    
    # Greeks calculation parameters
    GREEKS_PARAMS = {
        'risk_free_rate': 0.05,  # 5% risk-free rate
        'dividend_yield': 0.015,  # 1.5% dividend yield for SPX
        'trading_days_per_year': 252,
    }
    
    # Chart styling
    CHART_STYLE = {
        'figsize': (15, 10),
        'dpi': 300,
        'style': 'seaborn-v0_8',
        'color_palette': 'Set2',
        'font_size': 12,
    }
    
    # Report settings
    REPORT_SETTINGS = {
        'title': 'SPX Options Greeks Analysis Report',
        'author': 'Greeks Analysis Engine',
        'output_format': 'PDF',
        'include_charts': True,
        'chart_quality': 'high',
    }
    
    @classmethod
    def ensure_directories(cls):
        """Ensure all required directories exist"""
        cls.DATA_DIR.mkdir(exist_ok=True)
        cls.REPORTS_DIR.mkdir(exist_ok=True)
        cls.SRC_DIR.mkdir(exist_ok=True)
    
    @classmethod
    def get_output_path(cls, filename):
        """Get full path for output file"""
        return cls.REPORTS_DIR / filename
    
    @classmethod
    def get_data_path(cls, filename):
        """Get full path for data file"""
        return cls.DATA_DIR / filename

    @classmethod
    def find_latest_history_directory(cls) -> Optional[Tuple[str, str, str]]:
        """
        Find the most recent history directory in format {year}_{quarter}_option_chain
        Returns tuple of (directory_name, year, quarter) or None if not found
        """
        if not cls.DATA_DIR.exists():
            return None

        pattern = re.compile(r'^(\d{4})_(q[1-4])_option_chain$')
        directories = []

        for item in cls.DATA_DIR.iterdir():
            if item.is_dir():
                match = pattern.match(item.name)
                if match:
                    year, quarter = match.groups()
                    # Convert quarter to numeric for sorting (q1=1, q2=2, etc.)
                    quarter_num = int(quarter[1])
                    directories.append((item.name, int(year), quarter_num, year, quarter))

        if not directories:
            return None

        # Sort by year and quarter (most recent first)
        directories.sort(key=lambda x: (x[1], x[2]), reverse=True)
        latest = directories[0]

        return (latest[0], latest[3], latest[4])  # (directory_name, year_str, quarter_str)

    @classmethod
    def get_latest_data_file(cls) -> Optional[Path]:
        """
        Get the path to the most recent data file
        Format: {ticker}_complete_{year}_{quarter}.csv
        """
        latest_dir_info = cls.find_latest_history_directory()
        if not latest_dir_info:
            return None

        directory_name, year, quarter = latest_dir_info
        filename = f"{cls.TICKER.lower()}_complete_{year}_{quarter}.csv"
        file_path = cls.DATA_DIR / directory_name / filename

        if file_path.exists():
            return file_path
        else:
            return None

    @classmethod
    def initialize_data_file(cls):
        """Initialize the SPX_DATA_FILE with the latest available data file"""
        cls.SPX_DATA_FILE = cls.get_latest_data_file()
        return cls.SPX_DATA_FILE

