"""
Test script for SPX Report Generator
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))

from config import Config
from data_loader import SPXDataLoader
from analytics_engine import GreeksAnalyticsEngine
from report_generator import GreeksReportGenerator

def test_report_generator():
    """Test the report generator functionality"""
    print("Testing SPX Report Generator...")
    print("=" * 50)
    
    # Initialize and load data
    config = Config()
    config.ensure_directories()
    
    print("1. Loading and processing data...")
    loader = SPXDataLoader(config)
    loader.load_data()
    loader.clean_data()
    loader.calculate_derived_greeks()
    daily_data = loader.aggregate_daily_data()
    print(f"   ✓ Processed {len(daily_data)} days of data")
    
    # Run analytics
    print("\n2. Running analytics...")
    engine = GreeksAnalyticsEngine(config)
    engine.set_data(daily_data)
    analysis_results = engine.run_full_analysis()
    print("   ✓ Analytics completed")
    
    # Initialize report generator
    print("\n3. Initializing report generator...")
    report_gen = GreeksReportGenerator(config)
    report_gen.set_data(daily_data, analysis_results)
    print("   ✓ Report generator initialized")
    
    # Test individual chart generation
    print("\n4. Testing chart generation...")
    try:
        chart1 = report_gen.create_price_and_greeks_chart()
        print(f"   ✓ Price and Greeks chart: {os.path.basename(chart1)}")
        
        chart2 = report_gen.create_convergence_analysis_chart()
        print(f"   ✓ Convergence analysis chart: {os.path.basename(chart2)}")
        
        chart3 = report_gen.create_correlation_heatmap()
        if chart3:
            print(f"   ✓ Correlation heatmap: {os.path.basename(chart3)}")
        
        chart4 = report_gen.create_regime_analysis_chart()
        if chart4:
            print(f"   ✓ Regime analysis chart: {os.path.basename(chart4)}")
            
    except Exception as e:
        print(f"   ✗ Error generating charts: {e}")
        return False
    
    # Test markdown report generation
    print("\n5. Testing markdown report generation...")
    try:
        md_report = report_gen.generate_markdown_report()
        print(f"   ✓ Markdown report: {os.path.basename(md_report)}")
        
        # Check file size
        file_size = os.path.getsize(md_report)
        print(f"   ✓ Report size: {file_size:,} bytes")
        
    except Exception as e:
        print(f"   ✗ Error generating markdown report: {e}")
        return False
    
    # Test PDF generation
    print("\n6. Testing PDF report generation...")
    try:
        # Reset chart paths for full report
        report_gen.chart_paths = []
        pdf_report = report_gen.generate_pdf_report()
        print(f"   ✓ Final report: {os.path.basename(pdf_report)}")
        
        # Check if PDF was created
        if pdf_report.endswith('.pdf') and os.path.exists(pdf_report):
            file_size = os.path.getsize(pdf_report)
            print(f"   ✓ PDF size: {file_size:,} bytes")
        else:
            print(f"   ! Markdown report created instead: {os.path.basename(pdf_report)}")
            
    except Exception as e:
        print(f"   ✗ Error generating PDF report: {e}")
        return False
    
    # List all generated files
    print("\n7. Generated files:")
    reports_dir = config.REPORTS_DIR
    if os.path.exists(reports_dir):
        for file in os.listdir(reports_dir):
            file_path = os.path.join(reports_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"   - {file}: {size:,} bytes")
    
    print("\n" + "=" * 50)
    print("✓ All report generator tests passed!")
    return True

if __name__ == "__main__":
    success = test_report_generator()
    if not success:
        sys.exit(1)

