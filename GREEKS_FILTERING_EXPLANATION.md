# Greeks and Convergence Calculation with Future Expiry Filtering

## Overview

This document explains how Greeks and convergence events are calculated in the SPX Options Analysis system, and the improvements made to filter for strikes with expiry greater than today.

## Greeks Calculations

### Primary Greeks (from data)
- **Delta**: Price sensitivity to underlying movement
- **Gamma**: Rate of change of Delta
- **Vega**: Sensitivity to volatility changes
- **Theta**: Time decay
- **Rho**: Interest rate sensitivity

### Derived Greeks (calculated)
- **Vomma**: `Vega × Gamma × (Price/100)` - volatility of volatility
- **Vanna**: `Vega × Gamma / Price` - sensitivity of Delta to volatility changes  
- **Charm**: `-Theta × Gamma / Price` - rate of change of Delta with respect to time

### Open Interest Weighted Greeks
Each Greek is multiplied by Open Interest to get position-weighted exposure:
- `Delta_OI = Delta × Open Interest`
- `Gamma_OI = Gamma × Open Interest`
- etc.

## Convergence Event Detection

### Scoring System
Points are awarded for extreme conditions:
- **Extreme Vomma (>50M)**: +2 points
- **Extreme Vanna (>300K)**: +2 points  
- **High Open Interest (>1M)**: +1 point
- **Extreme Charm (>10M)**: +1 point
- **High Theta (>100M)**: +1 point
- **Extreme Delta P/C Ratio**: +1 point

### Event Classifications
- **Convergence (Score 4-5)**: Multiple extremes present
- **Major Convergence (Score 6-7)**: Significant institutional activity
- **Historic Convergence (Score 8+)**: Unprecedented positioning

## Filtering Improvements

### Problem with Previous Filtering
The system previously filtered `DTE >= 0`, which included:
- **Expired options** (DTE = 0) with zero time value
- **Same-day expiry** options with minimal time value
- These contribute meaningless Greeks values (often zeros)
- This diluted the analysis with stale data

### New Filtering Logic
Now filters for `Expiry Date > today` AND `DTE >= 1`:
- **Only future expiry dates**: Excludes all expired options
- **Minimum 1 day to expiration**: Excludes same-day expiry
- **Configurable parameters**: Can be adjusted in config

### Configuration Parameters
```python
ANALYSIS_PARAMS = {
    'filter_future_expiry_only': True,  # Only include options with expiry > today
    'min_dte': 1,                      # Minimum days to expiration
    # ... other parameters
}
```

### Impact of Filtering
**Example from recent run:**
- **Before filtering**: 1,500,612 rows loaded
- **After filtering**: 805,578 rows remaining
- **Filtered out**: 695,034 rows (46% reduction)

This removes nearly half the data that was expired or about to expire, significantly improving the quality of Greeks calculations.

## Benefits of Future Expiry Filtering

1. **More Accurate Greeks**: Eliminates zero/near-zero time value options
2. **Better Convergence Detection**: Focuses on active, tradeable options
3. **Cleaner Analysis**: Removes noise from expired positions
4. **Forward-Looking**: Analysis reflects current market positioning
5. **Configurable**: Can be adjusted based on analysis needs

## Usage

### Default Behavior
The system now automatically filters for future expiry dates by default.

### Customization
To change filtering behavior, modify `config/config.py`:

```python
# To include same-day expiry options
'min_dte': 0,

# To disable future expiry filtering (not recommended)
'filter_future_expiry_only': False,

# To require minimum 7 days to expiration
'min_dte': 7,
```

### Command Line
The filtering is applied automatically during data loading. No command line changes needed.

## Technical Details

### Implementation
- Filtering applied in `src/data_loader.py` during `clean_data()` method
- Uses `datetime.date.today()` for current date comparison
- Configurable through `ANALYSIS_PARAMS` in config
- Maintains backward compatibility with existing analysis pipeline

### Performance
- Reduces data volume by ~46% in typical cases
- Faster processing due to smaller dataset
- More focused analysis on relevant options

This improvement ensures that Greeks calculations and convergence analysis focus only on actively tradeable options with meaningful time value, providing more accurate and actionable insights.
