# SPX Options Greeks Analysis

A comprehensive Python analysis system for SPX options Greeks patterns, extreme events detection, and market regime identification. This system recreates the analysis described in the provided options trading documents, focusing on Vomma/Price divergence patterns, convergence events, and institutional distribution signals.

## Features

- **Modular Architecture**: Clean separation of data loading, analytics, and reporting
- **Advanced Greeks Calculations**: Vomma, Vanna, Charm, and traditional Greeks
- **Pattern Recognition**: Convergence events, distribution signals, market regimes
- **Comprehensive Reporting**: PDF reports with professional charts and analysis
- **Configurable Parameters**: Easy customization of thresholds and settings

## Project Structure

```
spx_greeks_analysis/
├── config/
│   └── config.py              # Configuration settings
├── src/
│   ├── data_loader.py         # Data loading and preprocessing
│   ├── analytics_engine.py    # Pattern recognition and analysis
│   └── report_generator.py    # Chart generation and PDF reports
├── data/
│   └── spx_complete_2025_q2.csv  # SPX options data
├── reports/                   # Generated reports and charts
├── requirements.txt           # Python dependencies
├── run_analysis.py           # Main execution script
└── README.md                 # This file
```

## Installation

1. **Clone or extract the project:**
   ```bash
   cd spx_greeks_analysis
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation:**
   ```bash
   python3.11 -c "import pandas, numpy, matplotlib, seaborn; print('All dependencies installed')"
   ```

## Usage

### Quick Start

Run the complete analysis with default settings:

```bash
python3.11 run_analysis.py
```

This will:
- Load the default SPX data file
- Process and calculate all Greeks
- Run pattern recognition analysis
- Generate a comprehensive PDF report

### Command Line Options

```bash
python3.11 run_analysis.py [options]

Options:
  -d, --data-file PATH    Specify custom data file path
  -o, --output-dir PATH   Specify output directory
  -v, --verbose          Enable verbose output
  -h, --help             Show help message
```

### Examples

```bash
# Run with custom data file
python3.11 run_analysis.py -d /path/to/custom_data.csv

# Run with verbose output
python3.11 run_analysis.py -v

# Run with custom output directory
python3.11 run_analysis.py -o /path/to/output/
```

## Data Format

The system expects CSV data with the following columns:

| Column | Description |
|--------|-------------|
| date | Trading date (YYYY-MM-DD) |
| Strike | Option strike price |
| Expiry Date | Option expiration date |
| Call/Put | Option type ('c' or 'p') |
| Open Interest | Number of open contracts |
| Volume | Trading volume |
| Delta | Option delta |
| Gamma | Option gamma |
| Vega | Option vega |
| Theta | Option theta |
| Rho | Option rho |
| spx_close | SPX closing price |

## Analysis Components

### 1. Data Loader (`data_loader.py`)

- **Data Validation**: Cleans and validates input data
- **Derived Greeks**: Calculates Vomma, Vanna, and Charm
- **Daily Aggregation**: Aggregates options data by trading day
- **Extreme Events**: Identifies days with extreme Greek values

### 2. Analytics Engine (`analytics_engine.py`)

- **Convergence Events**: Identifies when multiple Greeks reach extremes
- **Vomma/Price Divergence**: Detects distribution and acceleration patterns
- **Theta Decay Analysis**: Analyzes time decay urgency signals
- **Market Regimes**: Classifies market conditions
- **Correlation Analysis**: Calculates Greek-price relationships

### 3. Report Generator (`report_generator.py`)

- **Time Series Charts**: Price and Greeks over time
- **Convergence Analysis**: Visual analysis of extreme events
- **Correlation Heatmaps**: Greek relationship matrices
- **Regime Classification**: Market condition visualization
- **PDF Generation**: Professional report compilation

## Key Metrics

### Greeks Calculations

- **Vomma**: `Vega × Gamma × (Price/100)` - Volatility of volatility
- **Vanna**: `Vega × Gamma / Price` - Delta sensitivity to volatility
- **Charm**: `-Theta × Gamma / Price` - Delta decay over time

### Extreme Thresholds

- **Extreme Vomma**: >50 Million
- **Extreme Vanna**: >300 Thousand
- **Extreme Open Interest**: >1 Million
- **Extreme Charm**: >10 Million

### Convergence Scoring

Events are scored based on simultaneous extremes:
- **Score 4-5**: Convergence Event
- **Score 6-7**: Major Convergence
- **Score 8+**: Historic Convergence

## Configuration

Modify `config/config.py` to customize:

```python
ANALYSIS_PARAMS = {
    'extreme_vomma_threshold': 50_000_000,
    'extreme_vanna_threshold': 300_000,
    'extreme_oi_threshold': 1_000_000,
    'volume_collapse_threshold': 0.5,
}
```

## Output Files

The analysis generates several files in the `reports/` directory:

- **`spx_greeks_analysis_report.pdf`**: Main comprehensive report
- **`spx_greeks_analysis_report.md`**: Markdown version
- **`price_and_greeks_chart.png`**: Time series visualization
- **`convergence_analysis_chart.png`**: Extreme events analysis
- **`correlation_heatmap.png`**: Greek correlation matrix
- **`regime_analysis_chart.png`**: Market regime classification

## Understanding the Analysis

### Convergence Events

These occur when multiple Greeks reach extreme levels simultaneously, often indicating:
- Institutional positioning
- Market engineering
- Potential distribution or accumulation

### Vomma/Price Divergence

- **Distribution Pattern**: Price rises while Vomma explodes (selling signal)
- **Acceleration Pattern**: Price falls with negative Vomma (continuation signal)

### Market Regimes

- **Normal**: Standard Greek levels
- **Extreme**: Multiple Greeks at extremes
- **Distribution**: Price up + high Greeks
- **Collapse**: Negative Vomma + price down

## Technical Details

### Dependencies

- **pandas**: Data manipulation and analysis
- **numpy**: Numerical computations
- **matplotlib**: Chart generation
- **seaborn**: Statistical visualizations
- **scipy**: Statistical functions
- **reportlab/weasyprint**: PDF generation

### Performance

- Processes ~1.5M option records in under 30 seconds
- Generates comprehensive PDF report in under 60 seconds
- Memory usage typically under 500MB

## Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Data Format Errors**
   - Ensure CSV has required columns
   - Check date formats (YYYY-MM-DD)
   - Verify numeric columns contain valid numbers

3. **Memory Issues**
   - For large datasets, consider processing in chunks
   - Increase system memory if available

4. **PDF Generation Fails**
   - System falls back to Markdown report
   - Check `manus-md-to-pdf` utility availability

### Getting Help

For issues or questions:
1. Check the verbose output: `python3.11 run_analysis.py -v`
2. Review the generated markdown report for detailed findings
3. Examine individual test scripts for component-specific issues

## License

This project is provided as-is for educational and research purposes.

## Acknowledgments

Based on analysis patterns described in SPX options trading research documents focusing on extreme Greeks events and institutional distribution patterns.

