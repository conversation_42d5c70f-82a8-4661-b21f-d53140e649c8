"""
Data Loader module for SPX Options Greeks Analysis
Handles loading, cleaning, and preprocessing of options data
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class SPXDataLoader:
    """Data loader for SPX options data with Greeks calculations"""
    
    def __init__(self, config):
        self.config = config
        self.raw_data = None
        self.processed_data = None
        self.daily_aggregates = None
        
    def load_data(self, file_path: str = None) -> pd.DataFrame:
        """Load SPX options data from CSV file"""
        if file_path is None:
            # Initialize the data file path if not already set
            if self.config.SPX_DATA_FILE is None:
                self.config.initialize_data_file()

            file_path = self.config.SPX_DATA_FILE

            if file_path is None:
                raise Exception(f"No data file found. Please ensure the optionshistory directory exists with data in format: {self.config.TICKER.lower()}_complete_{{year}}_{{quarter}}.csv")

        try:
            print(f"Loading data from: {file_path}")
            self.raw_data = pd.read_csv(file_path)
            print(f"Loaded {len(self.raw_data)} rows of options data")
            return self.raw_data
        except Exception as e:
            raise Exception(f"Error loading data from {file_path}: {str(e)}")
    
    def clean_data(self) -> pd.DataFrame:
        """Clean and validate the loaded data"""
        if self.raw_data is None:
            raise ValueError("No data loaded. Call load_data() first.")
        
        # Convert date columns
        self.raw_data['date'] = pd.to_datetime(self.raw_data['date'])
        self.raw_data['Expiry Date'] = pd.to_datetime(self.raw_data['Expiry Date'])
        
        # Calculate days to expiration
        self.raw_data['DTE'] = (self.raw_data['Expiry Date'] - self.raw_data['date']).dt.days
        
        # Filter out invalid data
        self.raw_data = self.raw_data[
            (self.raw_data['DTE'] >= 0) &
            (self.raw_data['Open Interest'] >= 0) &
            (self.raw_data['Volume'] >= 0)
        ].copy()
        
        # Calculate moneyness
        self.raw_data['Moneyness'] = self.raw_data['Strike'] / self.raw_data['spx_close']
        
        print(f"Cleaned data: {len(self.raw_data)} rows remaining")
        return self.raw_data
    
    def calculate_derived_greeks(self) -> pd.DataFrame:
        """Calculate derived Greeks: Vomma, Vanna, Charm, and other metrics"""
        if self.raw_data is None:
            raise ValueError("No data available. Load and clean data first.")
        
        # Calculate Vomma (Volga) - second derivative of option value with respect to volatility
        # Approximation: Vomma ≈ Vega * (d(ln(S))/dσ) where S is underlying price
        self.raw_data['Vomma'] = self.raw_data['Vega'] * self.raw_data['Gamma'] * (self.raw_data['spx_close'] / 100)
        
        # Calculate Vanna - sensitivity of Delta to volatility changes
        # Approximation: Vanna ≈ Vega * Gamma / underlying_price
        self.raw_data['Vanna'] = self.raw_data['Vega'] * self.raw_data['Gamma'] / self.raw_data['spx_close']
        
        # Calculate Charm - rate of change of Delta with respect to time
        # Approximation: Charm ≈ -Theta * Gamma / underlying_price
        self.raw_data['Charm'] = -self.raw_data['Theta'] * self.raw_data['Gamma'] / self.raw_data['spx_close']
        
        # Calculate weighted Greeks by Open Interest
        self.raw_data['Delta_OI'] = self.raw_data['Delta'] * self.raw_data['Open Interest']
        self.raw_data['Gamma_OI'] = self.raw_data['Gamma'] * self.raw_data['Open Interest']
        self.raw_data['Vega_OI'] = self.raw_data['Vega'] * self.raw_data['Open Interest']
        self.raw_data['Theta_OI'] = self.raw_data['Theta'] * self.raw_data['Open Interest']
        self.raw_data['Vomma_OI'] = self.raw_data['Vomma'] * self.raw_data['Open Interest']
        self.raw_data['Vanna_OI'] = self.raw_data['Vanna'] * self.raw_data['Open Interest']
        self.raw_data['Charm_OI'] = self.raw_data['Charm'] * self.raw_data['Open Interest']
        
        # Calculate Put/Call ratio by Delta
        call_data = self.raw_data[self.raw_data['Call/Put'] == 'c'].copy()
        put_data = self.raw_data[self.raw_data['Call/Put'] == 'p'].copy()
        
        # Store processed data
        self.processed_data = self.raw_data.copy()
        
        print("Calculated derived Greeks: Vomma, Vanna, Charm")
        return self.processed_data
    
    def aggregate_daily_data(self) -> pd.DataFrame:
        """Aggregate options data by date to get daily Greeks totals"""
        if self.processed_data is None:
            raise ValueError("No processed data available. Process data first.")
        
        # Group by date and aggregate
        agg_functions = {
            'spx_open': 'first',
            'spx_high': 'first', 
            'spx_low': 'first',
            'spx_close': 'first',
            'Open Interest': 'sum',
            'Volume': 'sum',
            'Delta_OI': 'sum',
            'Gamma_OI': 'sum',
            'Vega_OI': 'sum',
            'Theta_OI': 'sum',
            'Vomma_OI': 'sum',
            'Vanna_OI': 'sum',
            'Charm_OI': 'sum',
        }
        
        self.daily_aggregates = self.processed_data.groupby('date').agg(agg_functions).reset_index()
        
        # Calculate Put/Call Delta ratio
        call_delta_oi = self.processed_data[self.processed_data['Call/Put'] == 'c'].groupby('date')['Delta_OI'].sum()
        put_delta_oi = self.processed_data[self.processed_data['Call/Put'] == 'p'].groupby('date')['Delta_OI'].sum()
        
        # Merge P/C ratio
        pc_ratio = pd.DataFrame({
            'date': call_delta_oi.index,
            'Call_Delta_OI': call_delta_oi.values,
            'Put_Delta_OI': put_delta_oi.values
        })
        pc_ratio['Delta_PC_Ratio'] = pc_ratio['Put_Delta_OI'] / (pc_ratio['Call_Delta_OI'] + 1e-10)  # Avoid division by zero
        
        self.daily_aggregates = self.daily_aggregates.merge(
            pc_ratio[['date', 'Delta_PC_Ratio']], on='date', how='left'
        )
        
        # Convert Greeks from OI-weighted to millions for readability
        greek_columns = ['Delta_OI', 'Gamma_OI', 'Vega_OI', 'Theta_OI', 'Vomma_OI', 'Vanna_OI', 'Charm_OI']
        for col in greek_columns:
            self.daily_aggregates[col] = self.daily_aggregates[col] / 1_000_000  # Convert to millions
        
        # Rename columns for clarity
        rename_dict = {
            'Delta_OI': 'Delta_M',
            'Gamma_OI': 'Gamma_M', 
            'Vega_OI': 'Vega_M',
            'Theta_OI': 'Theta_M',
            'Vomma_OI': 'Vomma_M',
            'Vanna_OI': 'Vanna_K',  # Vanna in thousands
            'Charm_OI': 'Charm_M',
            'Open Interest': 'Total_OI_K'  # OI in thousands
        }
        self.daily_aggregates.rename(columns=rename_dict, inplace=True)
        self.daily_aggregates['Total_OI_K'] = self.daily_aggregates['Total_OI_K'] / 1000  # Convert to thousands
        self.daily_aggregates['Vanna_K'] = self.daily_aggregates['Vanna_K'] * 1000  # Vanna in thousands
        
        print(f"Aggregated daily data: {len(self.daily_aggregates)} days")
        return self.daily_aggregates
    
    def get_extreme_events(self) -> Dict[str, pd.DataFrame]:
        """Identify extreme Greek events based on thresholds"""
        if self.daily_aggregates is None:
            raise ValueError("No daily aggregates available. Aggregate data first.")
        
        params = self.config.ANALYSIS_PARAMS
        
        extreme_events = {}
        
        # Extreme Vomma events
        extreme_events['vomma'] = self.daily_aggregates[
            abs(self.daily_aggregates['Vomma_M']) > params['extreme_vomma_threshold'] / 1_000_000
        ].copy()
        
        # Extreme Vanna events  
        extreme_events['vanna'] = self.daily_aggregates[
            abs(self.daily_aggregates['Vanna_K']) > params['extreme_vanna_threshold'] / 1000
        ].copy()
        
        # Extreme Charm events
        extreme_events['charm'] = self.daily_aggregates[
            abs(self.daily_aggregates['Charm_M']) > params['extreme_charm_threshold'] / 1_000_000
        ].copy()
        
        # Extreme OI events
        extreme_events['oi'] = self.daily_aggregates[
            self.daily_aggregates['Total_OI_K'] > params['extreme_oi_threshold'] / 1000
        ].copy()
        
        # Convergence events (multiple extremes on same day)
        convergence_mask = (
            (abs(self.daily_aggregates['Vomma_M']) > params['extreme_vomma_threshold'] / 1_000_000) &
            (abs(self.daily_aggregates['Vanna_K']) > params['extreme_vanna_threshold'] / 1000) &
            (self.daily_aggregates['Total_OI_K'] > params['extreme_oi_threshold'] / 1000)
        )
        extreme_events['convergence'] = self.daily_aggregates[convergence_mask].copy()
        
        print(f"Found extreme events:")
        for event_type, events in extreme_events.items():
            print(f"  {event_type}: {len(events)} events")
        
        return extreme_events
    
    def get_data_summary(self) -> Dict:
        """Get summary statistics of the loaded data"""
        if self.daily_aggregates is None:
            return {"error": "No data processed yet"}
        
        summary = {
            'total_days': len(self.daily_aggregates),
            'date_range': {
                'start': self.daily_aggregates['date'].min().strftime('%Y-%m-%d'),
                'end': self.daily_aggregates['date'].max().strftime('%Y-%m-%d')
            },
            'spx_price_range': {
                'min': self.daily_aggregates['spx_low'].min(),
                'max': self.daily_aggregates['spx_high'].max()
            },
            'greeks_summary': {
                'max_vomma_m': self.daily_aggregates['Vomma_M'].max(),
                'min_vomma_m': self.daily_aggregates['Vomma_M'].min(),
                'max_vanna_k': self.daily_aggregates['Vanna_K'].max(),
                'max_oi_k': self.daily_aggregates['Total_OI_K'].max(),
                'max_charm_m': self.daily_aggregates['Charm_M'].max(),
                'min_charm_m': self.daily_aggregates['Charm_M'].min(),
            }
        }
        
        return summary

