"""
Convergence Analysis Processor
Processes options data to identify convergence patterns and calculate metrics
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

from data_loader import SPXDataLoader
# Note: Using simplified approach without separate calculator classes

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConvergenceAnalyzer:
    """
    Analyzes options data for convergence patterns
    """

    def __init__(self, data_loader: SPXDataLoader):
        """
        Initialize convergence analyzer

        Args:
            data_loader: Instance of OptionsDataLoader
        """
        self.data_loader = data_loader
        self.convergence_data = None
    
    def calculate_daily_metrics(self, target_date: str) -> Dict[str, float]:
        """
        Calculate all convergence metrics for a specific date
        
        Args:
            target_date: Date string in YYYY-MM-DD format
            
        Returns:
            Dictionary with all calculated metrics
        """
        # Get options data for the date
        options_data = self.data_loader.get_data_by_date(target_date)
        
        if options_data.empty:
            logger.warning(f"No data available for {target_date}")
            return {}
        
        # Calculate portfolio Greeks directly from options data
        portfolio_greeks = self._calculate_portfolio_greeks(options_data)

        # Calculate additional metrics
        metrics = self._calculate_additional_metrics(options_data)

        # Combine all metrics
        all_metrics = {**portfolio_greeks, **metrics}
        all_metrics['date'] = target_date
        # Get spot price using dynamic column name
        ticker = getattr(self.data_loader, 'ticker', 'SPX')
        close_col = f'{ticker.lower()}_close'

        if close_col in options_data.columns:
            all_metrics['spot_price'] = options_data[close_col].iloc[0]
        else:
            # Fallback to common column names
            possible_cols = [f'{ticker.lower()}_close', 'close', 'underlying_price', 'spot_price']
            close_col = next((col for col in possible_cols if col in options_data.columns), None)
            if close_col:
                all_metrics['spot_price'] = options_data[close_col].iloc[0]
            else:
                raise ValueError(f"Could not find close price column for ticker {ticker}")
        
        return all_metrics

    def _calculate_portfolio_greeks(self, options_data: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate portfolio-level Greeks from options data

        Args:
            options_data: DataFrame with options data including Greeks

        Returns:
            Dictionary with portfolio Greeks
        """
        # Calculate portfolio Greeks by summing across all options
        portfolio_greeks = {
            'total_delta': options_data['Delta'].sum(),
            'total_gamma': options_data['Gamma'].sum(),
            'total_vega': options_data['Vega'].sum(),
            'total_theta': options_data['Theta'].sum(),
            'total_rho': options_data['Rho'].sum(),
            'total_vomma': options_data['Vomma'].sum(),
            'total_vanna': options_data['Vanna'].sum(),
            'total_charm': options_data['Charm'].sum(),
            'gex': self._calculate_gex(options_data),  # Gamma Exposure
            'total_oi': options_data['Open Interest'].sum()
        }

        return portfolio_greeks

    def _calculate_gex(self, options_data: pd.DataFrame) -> float:
        """Calculate Gamma Exposure using dynamic column name"""
        ticker = getattr(self.data_loader, 'ticker', 'SPX')
        close_col = f'{ticker.lower()}_close'

        if close_col in options_data.columns:
            spot_price = options_data[close_col].iloc[0]
        else:
            # Fallback to common column names
            possible_cols = [f'{ticker.lower()}_close', 'close', 'underlying_price', 'spot_price']
            close_col = next((col for col in possible_cols if col in options_data.columns), None)
            if close_col:
                spot_price = options_data[close_col].iloc[0]
            else:
                raise ValueError(f"Could not find close price column for ticker {ticker}")

        return options_data['Gamma'].sum() * spot_price ** 2 / 100

    def _calculate_additional_metrics(self, options_data: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate additional convergence metrics

        Args:
            options_data: Options data for the date

        Returns:
            Dictionary with additional metrics
        """
        metrics = {}
        
        # Calculate VolmBS (Volume-weighted flow)
        # Use Call/Put column to distinguish calls from puts
        if 'Call/Put' in options_data.columns:
            call_volume = options_data[options_data['Call/Put'] == 'C']['Volume'].sum() if 'Volume' in options_data.columns else 0
            put_volume = options_data[options_data['Call/Put'] == 'P']['Volume'].sum() if 'Volume' in options_data.columns else 0
            metrics['volm_bs'] = call_volume - put_volume
        else:
            metrics['volm_bs'] = 0
        
        # Calculate VxOI (Volatility x Open Interest proxy)
        # Using Vega as volatility proxy since IV may not be available
        total_oi = options_data['Open Interest'].sum()
        if total_oi > 0 and 'Vega' in options_data.columns:
            # Use Vega as volatility proxy weighted by open interest
            weighted_vega = (options_data['Vega'] * options_data['Open Interest']).sum() / total_oi
            metrics['vx_oi'] = weighted_vega * total_oi / 1000  # Scale for readability
        else:
            metrics['vx_oi'] = 0
        
        # Calculate Put/Call ratio by volume and OI
        if call_volume > 0:
            metrics['pc_ratio_volume'] = put_volume / call_volume
        else:
            metrics['pc_ratio_volume'] = 0
            
        call_oi = options_data[options_data['is_call'] == 1]['Open Interest'].sum()
        put_oi = options_data[options_data['is_call'] == 0]['Open Interest'].sum()
        if call_oi > 0:
            metrics['pc_ratio_oi'] = put_oi / call_oi
        else:
            metrics['pc_ratio_oi'] = 0
        
        # Calculate weighted average time to expiration
        if total_oi > 0:
            metrics['avg_tte'] = (options_data['tte'] * options_data['Open Interest']).sum() / total_oi
        else:
            metrics['avg_tte'] = 0
        
        # Calculate skew metrics
        otm_calls = options_data[(options_data['is_call'] == 1) & (options_data['moneyness'] > 1.02)]
        otm_puts = options_data[(options_data['is_call'] == 0) & (options_data['moneyness'] < 0.98)]
        
        if not otm_calls.empty and not otm_puts.empty:
            call_iv_avg = otm_calls['iv_mid'].mean()
            put_iv_avg = otm_puts['iv_mid'].mean()
            metrics['skew'] = put_iv_avg - call_iv_avg
        else:
            metrics['skew'] = 0
        
        return metrics
    
    def analyze_convergence_dates(self, convergence_dates: List[str]) -> pd.DataFrame:
        """
        Analyze all convergence dates and compile results
        
        Args:
            convergence_dates: List of convergence dates to analyze
            
        Returns:
            DataFrame with convergence analysis results
        """
        results = []
        
        for date in convergence_dates:
            logger.info(f"Analyzing convergence date: {date}")
            metrics = self.calculate_daily_metrics(date)
            
            if metrics:
                results.append(metrics)
            else:
                # If no data for exact date, try nearby dates
                nearby_metrics = self._find_nearby_date_metrics(date)
                if nearby_metrics:
                    results.append(nearby_metrics)
        
        if results:
            self.convergence_data = pd.DataFrame(results)
            return self.convergence_data
        else:
            logger.error("No convergence data could be calculated")
            return pd.DataFrame()
    
    def _find_nearby_date_metrics(self, target_date: str, max_days: int = 5) -> Optional[Dict[str, float]]:
        """
        Find metrics for a nearby date if exact date is not available
        
        Args:
            target_date: Target date string
            max_days: Maximum days to search around target date
            
        Returns:
            Metrics dictionary or None if no nearby data found
        """
        target_dt = pd.to_datetime(target_date)
        available_dates = self.data_loader.get_available_dates()
        
        # Find closest available date
        closest_date = None
        min_diff = timedelta(days=max_days + 1)
        
        for date in available_dates:
            diff = abs(date - target_dt)
            if diff < min_diff:
                min_diff = diff
                closest_date = date
        
        if closest_date and min_diff.days <= max_days:
            logger.info(f"Using nearby date {closest_date.strftime('%Y-%m-%d')} for target {target_date}")
            metrics = self.calculate_daily_metrics(closest_date.strftime('%Y-%m-%d'))
            metrics['original_target_date'] = target_date
            metrics['actual_date_used'] = closest_date.strftime('%Y-%m-%d')
            return metrics
        
        return None
    
    def calculate_convergence_signals(self) -> pd.DataFrame:
        """
        Calculate convergence signals based on the patterns from the document
        
        Returns:
            DataFrame with convergence signals
        """
        if self.convergence_data is None:
            logger.error("No convergence data available. Run analyze_convergence_dates first.")
            return pd.DataFrame()
        
        df = self.convergence_data.copy()
        
        # Scale Greeks to match document values (in millions/thousands)
        df['charm_scaled'] = df['total_charm'] / 1000  # Convert to thousands
        df['vanna_scaled'] = df['total_vanna'] / 1000  # Convert to thousands  
        df['gex_scaled'] = df['gex'] / 1000000  # Convert to millions
        df['vomma_scaled'] = df['total_vomma'] / 1000000  # Convert to millions
        df['volm_bs_scaled'] = df['volm_bs'] / 1000  # Convert to thousands
        df['vx_oi_scaled'] = df['vx_oi'] / 1000000  # Convert to millions
        
        # Calculate convergence signals based on document patterns
        df['bearish_signal'] = self._calculate_bearish_signal(df)
        df['bullish_signal'] = self._calculate_bullish_signal(df)
        df['explosive_signal'] = self._calculate_explosive_signal(df)
        
        # Overall signal strength
        df['signal_strength'] = df['explosive_signal'] * 3 + df['bullish_signal'] * 2 + df['bearish_signal'] * (-1)
        
        return df
    
    def _calculate_bearish_signal(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate bearish convergence signal
        Bearish Recipe: High positive charm + High positive GEX + Any vomma
        """
        bearish_charm = (df['charm_scaled'] > 100).astype(int)  # High positive charm
        bearish_gex = (df['gex_scaled'] > 100).astype(int)      # High positive GEX
        
        return (bearish_charm & bearish_gex).astype(int)
    
    def _calculate_bullish_signal(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate bullish convergence signal  
        Bullish Recipe: Negative vomma + High vanna + Low/negative GEX
        """
        bullish_vomma = (df['vomma_scaled'] < -50).astype(int)   # Negative vomma
        bullish_vanna = (df['vanna_scaled'] > 50).astype(int)    # High vanna
        bullish_gex = (df['gex_scaled'] < 50).astype(int)        # Low/negative GEX
        
        return (bullish_vomma & bullish_vanna & bullish_gex).astype(int)
    
    def _calculate_explosive_signal(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate explosive convergence signal
        Explosive Recipe: Extreme negative vomma + Extreme vanna + Moderate positive GEX + Negative charm
        """
        explosive_vomma = (df['vomma_scaled'] < -100).astype(int)  # Extreme negative vomma
        explosive_vanna = (df['vanna_scaled'] > 200).astype(int)   # Extreme vanna
        explosive_gex = ((df['gex_scaled'] > 10) & (df['gex_scaled'] < 200)).astype(int)  # Moderate positive GEX
        explosive_charm = (df['charm_scaled'] < 0).astype(int)     # Negative charm
        
        return (explosive_vomma & explosive_vanna & explosive_gex & explosive_charm).astype(int)
    
    def get_summary_report(self) -> Dict[str, any]:
        """
        Generate summary report of convergence analysis
        
        Returns:
            Dictionary with summary statistics and insights
        """
        if self.convergence_data is None:
            return {"error": "No convergence data available"}
        
        signals_df = self.calculate_convergence_signals()
        
        summary = {
            "total_dates_analyzed": len(signals_df),
            "bearish_signals": signals_df['bearish_signal'].sum(),
            "bullish_signals": signals_df['bullish_signal'].sum(), 
            "explosive_signals": signals_df['explosive_signal'].sum(),
            "average_metrics": {
                "charm": signals_df['charm_scaled'].mean(),
                "vanna": signals_df['vanna_scaled'].mean(),
                "gex": signals_df['gex_scaled'].mean(),
                "vomma": signals_df['vomma_scaled'].mean(),
                "volm_bs": signals_df['volm_bs_scaled'].mean(),
                "vx_oi": signals_df['vx_oi_scaled'].mean()
            },
            "extreme_values": {
                "max_charm": signals_df['charm_scaled'].max(),
                "min_charm": signals_df['charm_scaled'].min(),
                "max_vanna": signals_df['vanna_scaled'].max(),
                "min_vanna": signals_df['vanna_scaled'].min(),
                "max_gex": signals_df['gex_scaled'].max(),
                "min_gex": signals_df['gex_scaled'].min(),
                "max_vomma": signals_df['vomma_scaled'].max(),
                "min_vomma": signals_df['vomma_scaled'].min()
            }
        }
        
        return summary

