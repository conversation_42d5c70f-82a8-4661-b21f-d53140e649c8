"""
Black-Scholes Options Pricing and Greeks Calculation Module

This module provides accurate Black-Scholes calculations for option pricing
and Greeks (Delta, Gamma, Vega, Theta, Rho, V<PERSON>, Vanna, Charm).
"""

import numpy as np
import pandas as pd
from scipy.stats import norm
from typing import Union, Tuple
import warnings
warnings.filterwarnings('ignore')

class BlackScholesCalculator:
    """Black-Scholes options pricing and Greeks calculator"""
    
    def __init__(self, risk_free_rate: float = 0.05, dividend_yield: float = 0.015):
        """
        Initialize Black-Scholes calculator
        
        Args:
            risk_free_rate: Risk-free interest rate (default: 5%)
            dividend_yield: Dividend yield (default: 1.5% for SPX)
        """
        self.risk_free_rate = risk_free_rate
        self.dividend_yield = dividend_yield
    
    def _d1_d2(self, S: float, K: float, T: float, sigma: float) -> Tuple[float, float]:
        """
        Calculate d1 and d2 parameters for Black-Scholes formula
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            sigma: Implied volatility
            
        Returns:
            Tuple of (d1, d2)
        """
        if T <= 0:
            # Handle expired options
            if S > K:
                return (float('inf'), float('inf'))
            else:
                return (float('-inf'), float('-inf'))
        
        d1 = (np.log(S / K) + (self.risk_free_rate - self.dividend_yield + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        return d1, d2
    
    def option_price(self, S: float, K: float, T: float, sigma: float, option_type: str) -> float:
        """
        Calculate Black-Scholes option price
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            sigma: Implied volatility
            option_type: 'call' or 'put'
            
        Returns:
            Option price
        """
        if T <= 0:
            if option_type.lower() in ['call', 'c']:
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        d1, d2 = self._d1_d2(S, K, T, sigma)
        
        if option_type.lower() in ['call', 'c']:
            price = (S * np.exp(-self.dividend_yield * T) * norm.cdf(d1) - 
                    K * np.exp(-self.risk_free_rate * T) * norm.cdf(d2))
        else:  # put
            price = (K * np.exp(-self.risk_free_rate * T) * norm.cdf(-d2) - 
                    S * np.exp(-self.dividend_yield * T) * norm.cdf(-d1))
        
        return max(price, 0)  # Ensure non-negative price
    
    def delta(self, S: float, K: float, T: float, sigma: float, option_type: str) -> float:
        """Calculate Delta (price sensitivity to underlying)"""
        if T <= 0:
            if option_type.lower() in ['call', 'c']:
                return 1.0 if S > K else 0.0
            else:
                return -1.0 if S < K else 0.0
        
        d1, _ = self._d1_d2(S, K, T, sigma)
        
        if option_type.lower() in ['call', 'c']:
            return np.exp(-self.dividend_yield * T) * norm.cdf(d1)
        else:  # put
            return -np.exp(-self.dividend_yield * T) * norm.cdf(-d1)
    
    def gamma(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate Gamma (rate of change of Delta)"""
        if T <= 0:
            return 0.0
        
        d1, _ = self._d1_d2(S, K, T, sigma)
        
        return (np.exp(-self.dividend_yield * T) * norm.pdf(d1)) / (S * sigma * np.sqrt(T))
    
    def vega(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate Vega (sensitivity to volatility)"""
        if T <= 0:
            return 0.0
        
        d1, _ = self._d1_d2(S, K, T, sigma)
        
        return S * np.exp(-self.dividend_yield * T) * norm.pdf(d1) * np.sqrt(T) / 100  # Divide by 100 for 1% vol change
    
    def theta(self, S: float, K: float, T: float, sigma: float, option_type: str) -> float:
        """Calculate Theta (time decay)"""
        if T <= 0:
            return 0.0
        
        d1, d2 = self._d1_d2(S, K, T, sigma)
        
        term1 = -(S * np.exp(-self.dividend_yield * T) * norm.pdf(d1) * sigma) / (2 * np.sqrt(T))
        
        if option_type.lower() in ['call', 'c']:
            term2 = self.risk_free_rate * K * np.exp(-self.risk_free_rate * T) * norm.cdf(d2)
            term3 = -self.dividend_yield * S * np.exp(-self.dividend_yield * T) * norm.cdf(d1)
            theta_val = term1 - term2 + term3
        else:  # put
            term2 = -self.risk_free_rate * K * np.exp(-self.risk_free_rate * T) * norm.cdf(-d2)
            term3 = self.dividend_yield * S * np.exp(-self.dividend_yield * T) * norm.cdf(-d1)
            theta_val = term1 + term2 + term3
        
        return theta_val / 365  # Convert to daily theta
    
    def rho(self, S: float, K: float, T: float, sigma: float, option_type: str) -> float:
        """Calculate Rho (interest rate sensitivity)"""
        if T <= 0:
            return 0.0
        
        _, d2 = self._d1_d2(S, K, T, sigma)
        
        if option_type.lower() in ['call', 'c']:
            return K * T * np.exp(-self.risk_free_rate * T) * norm.cdf(d2) / 100  # Divide by 100 for 1% rate change
        else:  # put
            return -K * T * np.exp(-self.risk_free_rate * T) * norm.cdf(-d2) / 100
    
    def vomma(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate Vomma (Volga) - second derivative of option value with respect to volatility"""
        if T <= 0:
            return 0.0
        
        d1, d2 = self._d1_d2(S, K, T, sigma)
        vega_val = self.vega(S, K, T, sigma) * 100  # Get vega per unit vol change
        
        return vega_val * d1 * d2 / sigma
    
    def vanna(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate Vanna - sensitivity of Delta to volatility changes"""
        if T <= 0:
            return 0.0
        
        d1, d2 = self._d1_d2(S, K, T, sigma)
        
        return -np.exp(-self.dividend_yield * T) * norm.pdf(d1) * d2 / sigma / 100  # Divide by 100 for 1% vol change
    
    def charm(self, S: float, K: float, T: float, sigma: float, option_type: str) -> float:
        """Calculate Charm - rate of change of Delta with respect to time"""
        if T <= 0:
            return 0.0
        
        d1, d2 = self._d1_d2(S, K, T, sigma)
        
        if option_type.lower() in ['call', 'c']:
            term1 = self.dividend_yield * np.exp(-self.dividend_yield * T) * norm.cdf(d1)
            term2 = np.exp(-self.dividend_yield * T) * norm.pdf(d1) * (2 * (self.risk_free_rate - self.dividend_yield) * T - d2 * sigma * np.sqrt(T)) / (2 * T * sigma * np.sqrt(T))
            return -term1 - term2
        else:  # put
            term1 = -self.dividend_yield * np.exp(-self.dividend_yield * T) * norm.cdf(-d1)
            term2 = np.exp(-self.dividend_yield * T) * norm.pdf(d1) * (2 * (self.risk_free_rate - self.dividend_yield) * T - d2 * sigma * np.sqrt(T)) / (2 * T * sigma * np.sqrt(T))
            return term1 - term2
    
    def calculate_all_greeks(self, S: float, K: float, T: float, sigma: float, option_type: str) -> dict:
        """
        Calculate all Greeks for an option
        
        Returns:
            Dictionary containing all Greeks
        """
        return {
            'price': self.option_price(S, K, T, sigma, option_type),
            'delta': self.delta(S, K, T, sigma, option_type),
            'gamma': self.gamma(S, K, T, sigma),
            'vega': self.vega(S, K, T, sigma),
            'theta': self.theta(S, K, T, sigma, option_type),
            'rho': self.rho(S, K, T, sigma, option_type),
            'vomma': self.vomma(S, K, T, sigma),
            'vanna': self.vanna(S, K, T, sigma),
            'charm': self.charm(S, K, T, sigma, option_type)
        }

    def calculate_vectorized_greeks(self, df):
        """
        Vectorized calculation of all Greeks for a DataFrame

        Args:
            df: DataFrame with columns ['{ticker}_close', 'Strike', 'T', 'IV', 'Call/Put']

        Returns:
            DataFrame with calculated Greeks
        """
        # Find the close price column dynamically
        close_cols = [col for col in df.columns if col.endswith('_close')]
        if not close_cols:
            raise ValueError("No close price column found (expected format: {ticker}_close)")
        close_col = close_cols[0]

        # Convert to numpy arrays for vectorized operations
        S = df[close_col].values
        K = df['Strike'].values
        T = np.maximum(df['T'].values, 0)  # Ensure non-negative time
        sigma = df['IV'].values
        is_call = (df['Call/Put'] == 'c').values

        # Handle edge cases
        valid_mask = (T > 0) & (sigma > 0) & (S > 0) & (K > 0)

        # Initialize result arrays
        n = len(df)
        delta = np.zeros(n)
        gamma = np.zeros(n)
        vega = np.zeros(n)
        theta = np.zeros(n)
        rho = np.zeros(n)
        vomma = np.zeros(n)
        vanna = np.zeros(n)
        charm = np.zeros(n)

        if valid_mask.any():
            # Calculate d1 and d2 for valid options
            S_valid = S[valid_mask]
            K_valid = K[valid_mask]
            T_valid = T[valid_mask]
            sigma_valid = sigma[valid_mask]
            is_call_valid = is_call[valid_mask]

            d1 = (np.log(S_valid / K_valid) + (self.risk_free_rate - self.dividend_yield + 0.5 * sigma_valid**2) * T_valid) / (sigma_valid * np.sqrt(T_valid))
            d2 = d1 - sigma_valid * np.sqrt(T_valid)

            # Calculate Greeks for valid options
            exp_div_T = np.exp(-self.dividend_yield * T_valid)
            exp_rf_T = np.exp(-self.risk_free_rate * T_valid)
            norm_d1 = norm.cdf(d1)
            norm_d2 = norm.cdf(d2)
            norm_pdf_d1 = norm.pdf(d1)

            # Delta
            delta_valid = np.where(is_call_valid,
                                 exp_div_T * norm_d1,
                                 -exp_div_T * norm.cdf(-d1))
            delta[valid_mask] = delta_valid

            # Gamma (same for calls and puts)
            gamma_valid = (exp_div_T * norm_pdf_d1) / (S_valid * sigma_valid * np.sqrt(T_valid))
            gamma[valid_mask] = gamma_valid

            # Vega (same for calls and puts)
            vega_valid = S_valid * exp_div_T * norm_pdf_d1 * np.sqrt(T_valid) / 100
            vega[valid_mask] = vega_valid

            # Theta
            term1 = -(S_valid * exp_div_T * norm_pdf_d1 * sigma_valid) / (2 * np.sqrt(T_valid))
            theta_valid = np.where(is_call_valid,
                                 term1 - self.risk_free_rate * K_valid * exp_rf_T * norm_d2 - self.dividend_yield * S_valid * exp_div_T * norm_d1,
                                 term1 + self.risk_free_rate * K_valid * exp_rf_T * norm.cdf(-d2) + self.dividend_yield * S_valid * exp_div_T * norm.cdf(-d1))
            theta[valid_mask] = theta_valid / 365  # Convert to daily

            # Rho
            rho_valid = np.where(is_call_valid,
                               K_valid * T_valid * exp_rf_T * norm_d2 / 100,
                               -K_valid * T_valid * exp_rf_T * norm.cdf(-d2) / 100)
            rho[valid_mask] = rho_valid

            # Vomma
            vomma_valid = vega_valid * 100 * d1 * d2 / sigma_valid
            vomma[valid_mask] = vomma_valid

            # Vanna
            vanna_valid = -exp_div_T * norm_pdf_d1 * d2 / sigma_valid / 100
            vanna[valid_mask] = vanna_valid

            # Charm
            charm_term1 = np.where(is_call_valid,
                                 self.dividend_yield * exp_div_T * norm_d1,
                                 -self.dividend_yield * exp_div_T * norm.cdf(-d1))
            charm_term2 = exp_div_T * norm_pdf_d1 * (2 * (self.risk_free_rate - self.dividend_yield) * T_valid - d2 * sigma_valid * np.sqrt(T_valid)) / (2 * T_valid * sigma_valid * np.sqrt(T_valid))
            charm_valid = np.where(is_call_valid,
                                 -charm_term1 - charm_term2,
                                 charm_term1 - charm_term2)
            charm[valid_mask] = charm_valid

        return pd.DataFrame({
            'delta': delta,
            'gamma': gamma,
            'vega': vega,
            'theta': theta,
            'rho': rho,
            'vomma': vomma,
            'vanna': vanna,
            'charm': charm
        })
