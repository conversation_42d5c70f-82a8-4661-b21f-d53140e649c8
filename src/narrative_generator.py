"""
ChatGPT Narrative Generator for SPX Options Greeks Analysis
Generates dynamic narratives based on analysis results using OpenAI API
"""
import os
from typing import Dict, Any
from openai import OpenAI
from dotenv import load_dotenv
import json

class NarrativeGenerator:
    """
    Generate dynamic narratives using ChatGPT based on analysis results

    This class integrates with OpenAI's GPT-4 model to generate professional-quality
    financial narratives that adapt to the specific conditions detected in the options
    analysis. It provides three types of narratives:

    1. Executive Summary: High-level market analysis with actionable insights
    2. Convergence Analysis: Detailed interpretation of institutional activity patterns
    3. Regime Analysis: Forward-looking insights on market conditions

    Features:
    - Intelligent caching to minimize API costs
    - Professional financial language appropriate for institutional use
    - Dynamic content that changes based on actual analysis results
    - Robust error handling with fallback narratives
    """

    def __init__(self):
        """
        Initialize the narrative generator with OpenAI API

        Loads API key from .env file and sets up caching system for cost optimization.
        """
        # Load environment variables from .env file
        load_dotenv()

        # Initialize OpenAI client with API key
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY not found in .env file")

        self.client = OpenAI(api_key=api_key)

        # Initialize cache to avoid unnecessary API calls and reduce costs
        # Cache keys are based on analysis metrics to ensure fresh content when data changes
        self.cache = {}
        
    def _create_cache_key(self, analysis_results: Dict[str, Any]) -> str:
        """
        Create a cache key based on key analysis metrics

        This method generates a unique cache key based on the core analysis results.
        When the same analysis produces the same metrics, we can reuse cached narratives
        to avoid unnecessary API calls and reduce costs.

        Args:
            analysis_results: Dictionary containing analysis results

        Returns:
            String hash representing the key analysis metrics
        """
        # Extract key metrics that would affect narrative content
        key_metrics = {
            'convergence_events': len(analysis_results.get('convergence_events', [])),
            'distribution_signals': len(analysis_results.get('vomma_divergence', {}).get('distribution_signals', [])),
            'extreme_greek_days': len(analysis_results.get('processed_data', [])),
            'vomma_price_correlation': analysis_results.get('correlations', {}).get('vomma_price_correlation', 0)
        }
        return str(hash(json.dumps(key_metrics, sort_keys=True)))
    
    def generate_executive_summary(self, analysis_results: Dict[str, Any]) -> str:
        """Generate executive summary narrative using ChatGPT"""
        cache_key = f"exec_summary_{self._create_cache_key(analysis_results)}"
        
        if cache_key in self.cache:
            print("Using cached executive summary narrative")
            return self.cache[cache_key]
        
        # Extract key metrics for the prompt
        convergence_events = len(analysis_results.get('convergence_events', []))
        distribution_signals = len(analysis_results.get('vomma_divergence', {}).get('distribution_signals', []))
        vomma_correlation = analysis_results.get('correlations', {}).get('vomma_price_correlation', 0)
        
        # Get convergence thresholds if available
        thresholds = analysis_results.get('convergence_thresholds', {})
        vomma_threshold = thresholds.get('vomma_threshold', 0)
        vanna_threshold = thresholds.get('vanna_threshold', 0)
        
        prompt = f"""
        You are a professional options analyst writing an executive summary for SPX options Greeks analysis.
        
        Key Analysis Results:
        - Convergence Events: {convergence_events} (where multiple Greeks reach extreme levels simultaneously)
        - Distribution Signals: {distribution_signals} (Vomma/price divergence patterns)
        - Vomma-Price Correlation: {vomma_correlation:.3f}
        - Data-driven Vomma Threshold: {vomma_threshold:.1f}M
        - Data-driven Vanna Threshold: {vanna_threshold:.1f}K
        
        Write a concise 2-3 paragraph executive summary that:
        1. Interprets these metrics in terms of market conditions and institutional positioning
        2. Explains what convergence events and distribution signals mean for price movements
        3. Provides actionable insights for traders/investors
        4. Uses professional financial language but remains accessible
        
        Focus on the practical implications of these Greeks patterns for future price action.
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.7
            )
            
            narrative = response.choices[0].message.content.strip()
            self.cache[cache_key] = narrative
            return narrative
            
        except Exception as e:
            print(f"Error generating executive summary: {e}")
            return self._fallback_executive_summary(analysis_results)
    
    def generate_convergence_narrative(self, analysis_results: Dict[str, Any]) -> str:
        """Generate convergence events narrative using ChatGPT"""
        cache_key = f"convergence_{self._create_cache_key(analysis_results)}"
        
        if cache_key in self.cache:
            print("Using cached convergence narrative")
            return self.cache[cache_key]
        
        convergence_events = analysis_results.get('convergence_events', [])
        convergence_count = len(convergence_events)

        # Get event types if available (handle both DataFrame and dict formats)
        major_convergences = 0
        historic_convergences = 0

        if hasattr(convergence_events, 'iterrows'):  # DataFrame
            major_convergences = len(convergence_events[convergence_events['event_type'] == 'major_convergence'])
            historic_convergences = len(convergence_events[convergence_events['event_type'] == 'historic_convergence'])
        elif isinstance(convergence_events, list):  # List of dicts
            major_convergences = len([e for e in convergence_events if e.get('event_type') == 'major_convergence'])
            historic_convergences = len([e for e in convergence_events if e.get('event_type') == 'historic_convergence'])
        
        prompt = f"""
        You are analyzing SPX options convergence events for institutional traders.
        
        Analysis Results:
        - Total Convergence Events: {convergence_count}
        - Major Convergences: {major_convergences}
        - Historic Convergences: {historic_convergences}
        
        Convergence events occur when multiple Greeks (Vomma, Vanna, Charm, Theta) reach extreme levels simultaneously, 
        often indicating institutional positioning or market engineering.
        
        Write a 2-3 paragraph analysis that:
        1. Explains what these convergence patterns suggest about institutional activity
        2. Discusses the implications for future price volatility and direction
        3. Provides context on whether this level of convergence is typical or unusual
        4. Offers insights on potential trading opportunities or risks
        
        Use professional options trading terminology and focus on actionable intelligence.
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=400,
                temperature=0.7
            )
            
            narrative = response.choices[0].message.content.strip()
            self.cache[cache_key] = narrative
            return narrative
            
        except Exception as e:
            print(f"Error generating convergence narrative: {e}")
            return self._fallback_convergence_narrative(analysis_results)
    
    def generate_regime_narrative(self, analysis_results: Dict[str, Any]) -> str:
        """Generate market regime narrative using ChatGPT"""
        cache_key = f"regime_{self._create_cache_key(analysis_results)}"
        
        if cache_key in self.cache:
            print("Using cached regime narrative")
            return self.cache[cache_key]
        
        regimes = analysis_results.get('regimes', {})
        vol_regimes = regimes.get('volatility_regimes', {})
        greek_regimes = regimes.get('greek_regimes', {})
        
        prompt = f"""
        You are analyzing SPX options market regimes for professional traders.
        
        Regime Analysis:
        - Volatility Regimes: {vol_regimes}
        - Greek Regimes: {greek_regimes}
        
        Write a 2 paragraph analysis that:
        1. Interprets what these regime distributions suggest about current market conditions
        2. Explains how extreme Greek regimes relate to institutional positioning and price action
        3. Provides forward-looking insights on regime sustainability and potential transitions
        
        Focus on practical trading implications and market structure insights.
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=300,
                temperature=0.7
            )
            
            narrative = response.choices[0].message.content.strip()
            self.cache[cache_key] = narrative
            return narrative
            
        except Exception as e:
            print(f"Error generating regime narrative: {e}")
            return self._fallback_regime_narrative(analysis_results)
    
    def _fallback_executive_summary(self, analysis_results: Dict[str, Any]) -> str:
        """Fallback executive summary if ChatGPT fails"""
        convergence_events = len(analysis_results.get('convergence_events', []))
        distribution_signals = len(analysis_results.get('vomma_divergence', {}).get('distribution_signals', []))
        ticker = self.config.TICKER

        return f"""
        **Market Analysis Summary**

        The {ticker} options analysis reveals {convergence_events} convergence events where multiple Greeks reached extreme levels simultaneously, suggesting significant institutional positioning. With {distribution_signals} distribution signals detected, the market shows signs of potential volatility expansion or contraction patterns.
        
        The data-driven threshold analysis provides more accurate extreme event detection compared to static thresholds, enabling better identification of institutional flow patterns and potential price inflection points.
        """
    
    def _fallback_convergence_narrative(self, analysis_results: Dict[str, Any]) -> str:
        """Fallback convergence narrative if ChatGPT fails"""
        convergence_count = len(analysis_results.get('convergence_events', []))
        
        return f"""
        **Convergence Events Analysis**
        
        {convergence_count} convergence events were identified using data-driven thresholds. These events represent periods when multiple Greeks reached extreme levels simultaneously, often indicating institutional positioning or market engineering activities.
        
        Such convergence patterns typically precede significant price movements and should be monitored closely for potential trading opportunities.
        """
    
    def _fallback_regime_narrative(self, analysis_results: Dict[str, Any]) -> str:
        """Fallback regime narrative if ChatGPT fails"""
        return """
        **Market Regime Analysis**
        
        The regime analysis categorizes market conditions based on volatility and Greek extremes. Extreme Greek regimes often coincide with institutional positioning and can signal potential price inflection points.
        
        Understanding regime transitions helps in positioning for volatility expansion or contraction cycles.
        """
