"""
Report Generator for SPX Options Greeks Analysis
Creates comprehensive charts and PDF reports
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from src.narrative_generator import NarrativeGenerator

class GreeksReportGenerator:
    """Report generator for Greeks analysis with charts and PDF output"""
    
    def __init__(self, config):
        self.config = config
        self.data = None
        self.analysis_results = None
        self.chart_paths = []

        # Initialize narrative generator
        try:
            self.narrative_generator = NarrativeGenerator()
            print("✓ ChatGPT narrative generation enabled")
        except Exception as e:
            print(f"⚠ ChatGPT narrative generation disabled: {e}")
            self.narrative_generator = None
        
    def set_data(self, daily_data: pd.DataFrame, analysis_results: Dict):
        """Set the data and analysis results for report generation"""
        # Use processed data with extreme scores if available
        if 'processed_data' in analysis_results:
            self.data = analysis_results['processed_data'].copy()
        else:
            self.data = daily_data.copy()
            # Add basic extreme_score if not present
            if 'extreme_score' not in self.data.columns:
                self.data['extreme_score'] = 0
        
        self.data['date'] = pd.to_datetime(self.data['date'])
        self.analysis_results = analysis_results
        
        # Add price_change if not present using dynamic column name
        if 'price_change' not in self.data.columns:
            ticker = self.config.TICKER.lower()
            close_col = f'{ticker}_close'
            if close_col in self.data.columns:
                self.data['price_change'] = self.data[close_col].diff()
            else:
                # Fallback for compatibility
                self.data['price_change'] = 0
        
        # Set up matplotlib style
        plt.style.use('default')
        sns.set_palette("Set2")
        
    def create_price_and_greeks_chart(self) -> str:
        """Create forward-looking expiry date analysis chart showing Greeks by future expiry dates"""
        # Use configuration constants for chart dimensions and styling
        chart_config = self.config.CHART_CONFIG

        # Get raw data for expiry date analysis
        if 'raw_data_sample' not in self.analysis_results:
            print("No raw data available for expiry date analysis, falling back to daily data")
            recent_data = self.data.tail(30).copy()
            use_expiry_analysis = False
        else:
            raw_data = self.analysis_results['raw_data_sample']

            # Filter for FUTURE expiry dates only (next 30 days)
            today = pd.Timestamp.now().date()
            thirty_days_out = today + pd.Timedelta(days=30)

            future_data = raw_data[
                (raw_data['Expiry Date'].dt.date > today) &
                (raw_data['Expiry Date'].dt.date <= thirty_days_out)
            ].copy()

            if len(future_data) > 0:
                # Group by expiry date to create forward-looking analysis
                expiry_analysis = future_data.groupby('Expiry Date').agg({
                    'Vomma': 'sum',
                    'Vanna': 'sum',
                    'Charm': 'sum',
                    'Theta': 'sum',
                    'Delta': 'sum',
                    'Gamma': 'sum',
                    'Open Interest': 'sum',
                    'Strike': ['min', 'max', 'count'],
                    f'{self.config.TICKER.lower()}_close': 'first',
                    'DTE': 'first'
                }).reset_index()

                # Flatten column names
                # Dynamic column naming for ticker-specific close price
                ticker_close_col = f'{self.config.TICKER}_Close'
                expiry_analysis.columns = ['Expiry_Date', 'Vomma_Sum', 'Vanna_Sum', 'Charm_Sum',
                                         'Theta_Sum', 'Delta_Sum', 'Gamma_Sum', 'OI_Sum',
                                         'Strike_Min', 'Strike_Max', 'Strike_Count', ticker_close_col, 'DTE']

                recent_data = expiry_analysis
                use_expiry_analysis = True
                print(f"Price/Greeks chart: analyzing {len(recent_data)} future expiry dates from {today} to {thirty_days_out}")
            else:
                print("No future expiry data available, using recent daily data")
                recent_data = self.data.tail(30).copy()
                use_expiry_analysis = False

        fig, axes = plt.subplots(4, 1, figsize=chart_config['figure_size_extra_large'])

        ticker = self.config.TICKER
        if use_expiry_analysis:
            fig.suptitle(f'📈 {ticker} Greeks by Future Expiry Dates (Next 30 Days) 📈',
                        fontsize=chart_config['font_size_title'], fontweight='bold')
        else:
            fig.suptitle(f'📈 {ticker} Price and Greeks - Recent Daily Data 📈',
                        fontsize=chart_config['font_size_title'], fontweight='bold')

        # Chart 1: Price or Greeks concentration by expiry
        ax1 = axes[0]
        colors = chart_config['colors']

        if use_expiry_analysis:
            # Show current price as horizontal line and Greeks concentration by expiry
            ticker = self.config.TICKER
            close_col = f'{ticker}_Close'
            current_price = recent_data[close_col].iloc[0] if len(recent_data) > 0 and close_col in recent_data.columns else 5500
            ax1.axhline(y=current_price, color=colors['primary_blue'], linewidth=2,
                       label=f'Current {ticker}: {current_price:.0f}', linestyle='--')

            # Show Vomma concentration by expiry date
            ax1_twin = ax1.twinx()
            ax1_twin.bar(recent_data['Expiry_Date'], recent_data['Vomma_Sum'] / 1000000,
                        alpha=0.6, color=colors['primary_red'], label='Vomma Concentration (M)')
            ax1_twin.set_ylabel('Vomma (Millions)')
            ax1_twin.legend(loc='upper right')
        else:
            ticker = self.config.TICKER.lower()
            close_col = f'{ticker}_close'
            if close_col in recent_data.columns:
                ax1.plot(recent_data['date'], recent_data[close_col],
                        linewidth=chart_config['line_width'], color=colors['primary_blue'],
                        label=f'{self.config.TICKER} Close')

        # Mark convergence events
        convergence_events = self.analysis_results.get('convergence_events', pd.DataFrame())
        if len(convergence_events) > 0 and use_expiry_analysis:
            # Forward convergence events - highlight convergence expiry dates
            for _, event in convergence_events.iterrows():
                expiry_date = event['Expiry Date']
                # Highlight convergence expiry dates with different colors
                if expiry_date in recent_data['Expiry_Date'].values:
                    ax1.axvline(x=expiry_date, color='red', linestyle='--', alpha=0.8,
                               label=f'Convergence Expiry ({expiry_date.strftime("%m/%d")})')

        if use_expiry_analysis:
            ax1.set_title(f'Current {ticker} Price vs Future Greeks Concentration by Expiry')
            ax1.set_ylabel(f'{ticker} Price Level')
        else:
            ax1.set_title(f'{ticker} Price - Recent Daily Data')
            ax1.set_ylabel(f'{ticker} Price')

        ax1.legend()
        ax1.grid(True, alpha=chart_config['grid_alpha'])

        # Chart 2: Vomma and Vanna by expiry or recent trends
        ax2 = axes[1]
        ax2_twin = ax2.twinx()

        if use_expiry_analysis:
            # Show Vomma and Vanna concentration by expiry date
            x_data = recent_data['Expiry_Date']
            vomma_data = recent_data['Vomma_Sum'] / 1000000  # Convert to millions
            vanna_data = recent_data['Vanna_Sum'] / 1000     # Convert to thousands

            line1 = ax2.plot(x_data, vomma_data, color=colors['primary_red'],
                           linewidth=chart_config['line_width'], marker='o', label='Vomma (M)')
            line2 = ax2_twin.plot(x_data, vanna_data, color=colors['primary_green'],
                                linewidth=chart_config['line_width'], marker='s', label='Vanna (K)')
            ax2.set_title('Greeks Concentration by Future Expiry Date')
        else:
            # Show daily historical data
            line1 = ax2.plot(recent_data['date'], recent_data['Vomma_M'],
                            color=colors['primary_red'], linewidth=chart_config['line_width'], label='Vomma (M)')
            line2 = ax2_twin.plot(recent_data['date'], recent_data['Vanna_K'],
                                 color=colors['primary_green'], linewidth=chart_config['line_width'], label='Vanna (K)')
            ax2.set_title('Vomma and Vanna - Daily Trends')

        ax2.set_ylabel('Vomma (Millions)', color=colors['primary_red'])
        ax2_twin.set_ylabel('Vanna (Thousands)', color=colors['primary_green'])
        ax2.tick_params(axis='y', labelcolor=colors['primary_red'])
        ax2_twin.tick_params(axis='y', labelcolor=colors['primary_green'])

        # Combine legends
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='upper left')
        ax2.grid(True, alpha=chart_config['grid_alpha'])
        
        # Chart 3: Theta and Charm by expiry or time
        ax3 = axes[2]
        ax3_twin = ax3.twinx()

        if use_expiry_analysis:
            # Show Theta and Charm concentration by expiry date
            x_data = recent_data['Expiry_Date']
            theta_data = recent_data['Theta_Sum'] / 1000000  # Convert to millions
            charm_data = recent_data['Charm_Sum'] / 1000000  # Convert to millions

            line3 = ax3.plot(x_data, theta_data, color=colors['primary_purple'],
                           linewidth=chart_config['line_width'], marker='o', label='Theta (M)')
            line4 = ax3_twin.plot(x_data, charm_data, color=colors['primary_brown'],
                                linewidth=chart_config['line_width'], marker='s', label='Charm (M)')
            ax3.set_title('Theta and Charm Concentration by Future Expiry Date')
        else:
            # Show daily historical data
            line3 = ax3.plot(recent_data['date'], recent_data['Theta_M'],
                            color=colors['primary_purple'], linewidth=chart_config['line_width'], label='Theta (M)')
            line4 = ax3_twin.plot(recent_data['date'], recent_data['Charm_M'],
                                 color=colors['primary_brown'], linewidth=chart_config['line_width'], label='Charm (M)')
            ax3.set_title('Theta and Charm - Daily Trends')

        ax3.set_ylabel('Theta (Millions)', color=colors['primary_purple'])
        ax3_twin.set_ylabel('Charm (Millions)', color=colors['primary_brown'])
        ax3.tick_params(axis='y', labelcolor=colors['primary_purple'])
        ax3_twin.tick_params(axis='y', labelcolor=colors['primary_brown'])

        # Combine legends
        lines = line3 + line4
        labels = [l.get_label() for l in lines]
        ax3.legend(lines, labels, loc='upper left')
        ax3.grid(True, alpha=chart_config['grid_alpha'])
        
        # Chart 4: Open Interest and Strike Distribution by expiry
        ax4 = axes[3]
        ax4_twin = ax4.twinx()

        if use_expiry_analysis:
            # Show Open Interest and Strike Count by expiry date
            x_data = recent_data['Expiry_Date']
            oi_data = recent_data['OI_Sum'] / 1000  # Convert to thousands
            strike_count = recent_data['Strike_Count']

            line5 = ax4.bar(x_data, oi_data, alpha=chart_config['bar_alpha'],
                           color=colors['primary_cyan'], label='Open Interest (K)')
            line6 = ax4_twin.plot(x_data, strike_count, color=colors['primary_orange'],
                                linewidth=chart_config['line_width'], marker='o', label='Strike Count')
            ax4.set_title('Open Interest and Strike Distribution by Future Expiry Date')
            ax4_twin.set_ylabel('Number of Strikes', color=colors['primary_orange'])
            ax4.set_xlabel('Expiry Date')
        else:
            # Show daily historical data
            line5 = ax4.bar(recent_data['date'], recent_data['Total_OI_K'],
                           alpha=chart_config['bar_alpha'], color=colors['primary_cyan'], label='Open Interest (K)')
            line6 = ax4_twin.plot(recent_data['date'], recent_data['Volume'],
                                 color=colors['primary_orange'], linewidth=chart_config['line_width'], label='Volume')
            ax4.set_title('Open Interest and Volume - Daily Data')
            ax4_twin.set_ylabel('Volume', color=colors['primary_orange'])
            ax4.set_xlabel('Date')

        ax4.set_ylabel('Open Interest (Thousands)', color=colors['primary_cyan'])
        ax4.tick_params(axis='y', labelcolor=colors['primary_cyan'])
        ax4_twin.tick_params(axis='y', labelcolor=colors['primary_orange'])

        # Format x-axis using configuration constants
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter(chart_config['date_format']))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=chart_config['date_interval_weekly']))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=chart_config['rotation_angle'])

        plt.tight_layout()

        # Save chart using configuration constants
        chart_path = self.config.get_output_path(chart_config['chart_files']['price_and_greeks'])
        plt.savefig(chart_path, dpi=chart_config['dpi'], bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def create_convergence_analysis_chart(self) -> str:
        """Create convergence events analysis chart showing all data with convergence events"""
        # Use configuration constants for chart dimensions and styling
        chart_config = self.config.CHART_CONFIG
        colors = chart_config['colors']

        # Check if we have convergence events to determine time window
        convergence_events = self.analysis_results.get('convergence_events', pd.DataFrame())

        if len(convergence_events) > 0:
            # Handle different date structures for forward vs historical convergence
            if 'Expiry Date' in convergence_events.columns:
                # Forward convergence events - create a timeline that includes both historical data and future expiry dates
                recent_data = self.data.tail(30).copy()
                start_date = recent_data['date'].min().date()

                # Extend timeline to include convergence expiry dates
                latest_expiry = convergence_events['Expiry Date'].max().date()
                end_date = max(recent_data['date'].max().date(), latest_expiry)

                print(f"Convergence chart: showing timeline from {start_date} to {end_date} with {len(convergence_events)} forward convergence events")

                # Create extended timeline for forward convergence visualization
                # We'll show historical data + markers for future convergence expiry dates

            else:
                # Historical convergence events - show data around event dates
                earliest_event = convergence_events['date'].min()
                latest_event = convergence_events['date'].max()
                start_date = (earliest_event - pd.Timedelta(days=10)).date()
                end_date = (latest_event + pd.Timedelta(days=10)).date()
                recent_data = self.data[
                    (self.data['date'].dt.date >= start_date) &
                    (self.data['date'].dt.date <= end_date)
                ].copy()
                print(f"Convergence chart: showing data from {start_date} to {end_date} to include {len(convergence_events)} events")
        else:
            # If no convergence events, show recent data (last 30 days)
            today = pd.Timestamp.now().date()
            thirty_days_ago = today - pd.Timedelta(days=30)
            recent_data = self.data[self.data['date'].dt.date >= thirty_days_ago].copy()

        if len(recent_data) == 0:
            # If no data in range, use last 10 data points
            recent_data = self.data.tail(10).copy()

        fig, axes = plt.subplots(2, 2, figsize=chart_config['figure_size_medium'])
        fig.suptitle('📊 Convergence Events Analysis - RECENT TRENDS (Last 30 Days) 📊',
                    fontsize=chart_config['font_size_title'], fontweight='bold')

        # Chart 1: Extreme Score over time (recent)
        ax1 = axes[0, 0]
        ax1.plot(recent_data['date'], recent_data['extreme_score'],
                linewidth=chart_config['line_width'], color=colors['primary_red'],
                marker='o', markersize=chart_config['marker_size'])
        ax1.axhline(y=chart_config['convergence_threshold'], color=colors['primary_orange'],
                   linestyle='--', alpha=chart_config['line_alpha'], label='Convergence Threshold')
        ax1.axhline(y=chart_config['major_convergence_threshold'], color=colors['primary_red'],
                   linestyle='--', alpha=chart_config['line_alpha'], label='Major Convergence')
        ax1.set_title('Extreme Score Timeline')
        ax1.set_ylabel('Extreme Score')
        ax1.legend()
        ax1.grid(True, alpha=chart_config['grid_alpha'])

        # Mark convergence events on the extreme score chart
        if len(convergence_events) > 0:
            if 'Expiry Date' in convergence_events.columns:
                # Forward convergence events - create a separate subplot to show future convergence events
                # Since convergence events are future expiry dates, we'll add them as annotations

                # Add a second y-axis to show convergence event scores
                ax2 = ax1.twinx()

                # Plot convergence events as scatter points with their scores
                expiry_dates = convergence_events['Expiry Date']
                scores = convergence_events['extreme_score']

                # Create scatter plot for convergence events
                scatter = ax2.scatter(expiry_dates, scores,
                                    c=['red' if score >= 12 else 'orange' if score >= 9 else 'yellow' for score in scores],
                                    s=100, alpha=0.8, marker='o', edgecolors='black', linewidth=1,
                                    label='Convergence Events')

                # Add vertical lines for each convergence event
                for _, event in convergence_events.iterrows():
                    expiry_date = event['Expiry Date']
                    score = event['extreme_score']
                    event_type = event['event_type'].replace('_', ' ').title()

                    ax1.axvline(x=expiry_date, color='red', linestyle='--', alpha=0.6)

                    # Add text annotation for each event
                    ax2.annotate(f'{event_type}\nScore: {score}\n{expiry_date.strftime("%m/%d")}',
                               xy=(expiry_date, score), xytext=(10, 10),
                               textcoords='offset points', fontsize=8,
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                               arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

                ax2.set_ylabel('Convergence Score', color='red')
                ax2.tick_params(axis='y', labelcolor='red')
                ax2.set_ylim(0, max(scores) + 2)

            else:
                # Historical convergence events - mark event dates
                data_start = recent_data['date'].min().date()
                data_end = recent_data['date'].max().date()
                visible_events = convergence_events[
                    (convergence_events['date'].dt.date >= data_start) &
                    (convergence_events['date'].dt.date <= data_end)
                ]
                for _, event in visible_events.iterrows():
                    ax1.axvline(x=event['date'], color='red', linestyle=':', alpha=0.8,
                               label=f'Convergence ({event["date"].strftime("%m/%d")})')
        
        # Chart 2: Vomma vs Price Change using raw option data
        ax2 = axes[0, 1]

        # Use raw option data to create meaningful Vomma vs Price scatter plot
        if 'raw_data_sample' in self.analysis_results and len(self.analysis_results['raw_data_sample']) > 100:
            raw_data = self.analysis_results['raw_data_sample']

            # Calculate price changes and Vomma for each option
            ticker_close_col = f'{self.config.TICKER.lower()}_close'

            # Sample options for scatter plot (avoid overcrowding)
            sample_size = min(1000, len(raw_data))
            sample_data = raw_data.sample(n=sample_size, random_state=42).copy()

            # Calculate price distance from current price
            current_price = sample_data[ticker_close_col].iloc[0]
            sample_data['price_distance'] = sample_data['Strike'] - current_price

            # Convert Vomma to millions for display
            sample_data['Vomma_M'] = sample_data['Vomma'] / 1000000

            # Create color mapping based on DTE (Days to Expiry)
            scatter = ax2.scatter(sample_data['price_distance'], sample_data['Vomma_M'],
                                 c=sample_data['DTE'], cmap='viridis',
                                 alpha=chart_config['scatter_alpha'], s=chart_config['scatter_size'])
            ax2.set_title('Vomma vs Strike Distance from Current Price')
            ax2.set_xlabel('Strike Distance from Current Price')
            ax2.set_ylabel('Vomma (M)')
            ax2.grid(True, alpha=chart_config['grid_alpha'])
            plt.colorbar(scatter, ax=ax2, label='Days to Expiry')

            print(f"Vomma scatter: using {len(sample_data)} option samples")
        else:
            # Fallback to daily aggregated data if available
            if len(self.data) > 1 and 'price_change' in self.data.columns:
                scatter = ax2.scatter(self.data['price_change'], self.data['Vomma_M'],
                                     c=self.data['extreme_score'], cmap='Reds',
                                     alpha=chart_config['scatter_alpha'], s=chart_config['scatter_size'])
                ax2.set_title('Vomma vs Price Change (Daily Data)')
                ax2.set_xlabel('Price Change')
                ax2.set_ylabel('Vomma (M)')
                ax2.grid(True, alpha=chart_config['grid_alpha'])
                plt.colorbar(scatter, ax=ax2, label='Extreme Score')
            else:
                ax2.text(0.5, 0.5, 'Insufficient Data for\nVomma vs Price Analysis', ha='center', va='center',
                        transform=ax2.transAxes, fontsize=12,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.7))
                ax2.set_title('Vomma vs Price Change - Insufficient Data')
                ax2.axis('off')

        # Chart 3: Delta P/C Ratio using raw option data for better coverage
        ax3 = axes[1, 0]

        # Calculate P/C ratio from raw option data if available
        if 'raw_data_sample' in self.analysis_results:
            raw_data = self.analysis_results['raw_data_sample']

            # Filter for recent option data (last 30 days of trading data)
            today = pd.Timestamp.now().date()
            thirty_days_ago = today - pd.Timedelta(days=30)

            # Group by date and calculate P/C ratio from raw options
            pc_raw_data = raw_data[raw_data['date'].dt.date >= thirty_days_ago].copy()

            if len(pc_raw_data) > 0:
                # Calculate daily P/C ratios from raw option data
                daily_pc = pc_raw_data.groupby('date').apply(lambda x:
                    x[x['Call/Put'] == 'p']['Delta'].sum() /
                    (x[x['Call/Put'] == 'c']['Delta'].sum() + 1e-10)
                ).reset_index()
                daily_pc.columns = ['date', 'Delta_PC_Ratio']
                daily_pc = daily_pc[daily_pc['Delta_PC_Ratio'].notna() & (daily_pc['Delta_PC_Ratio'] != 0)]

                if len(daily_pc) > 0:
                    pc_data = daily_pc
                    print(f"P/C chart: calculated from raw options data - {len(pc_data)} data points from {pc_data['date'].min().date()} to {pc_data['date'].max().date()}")
                else:
                    # Fallback to aggregated data
                    pc_data = self.data[self.data['Delta_PC_Ratio'].notna() & (self.data['Delta_PC_Ratio'] != 0)].tail(20).copy()
                    print(f"P/C chart: using aggregated data fallback - {len(pc_data)} points")
            else:
                # Fallback to aggregated data
                pc_data = self.data[self.data['Delta_PC_Ratio'].notna() & (self.data['Delta_PC_Ratio'] != 0)].tail(20).copy()
                print(f"P/C chart: using aggregated data fallback - {len(pc_data)} points")
        else:
            # Use recent data for P/C ratio, but ensure we have meaningful data
            pc_data = recent_data[recent_data['Delta_PC_Ratio'].notna() & (recent_data['Delta_PC_Ratio'] != 0)].copy()

            # If insufficient P/C data in recent period, expand to get at least 15 meaningful points
            if len(pc_data) < 10:
                print(f"Limited P/C data in recent period ({len(pc_data)} points), expanding to get meaningful data")
                pc_data = self.data[self.data['Delta_PC_Ratio'].notna() & (self.data['Delta_PC_Ratio'] != 0)].tail(20).copy()

            print(f"P/C chart: using {len(pc_data)} data points from {pc_data['date'].min().date()} to {pc_data['date'].max().date()}")

        if len(pc_data) > 0:
            ax3.plot(pc_data['date'], pc_data['Delta_PC_Ratio'],
                    linewidth=chart_config['line_width'], color=colors['primary_green'])
            ax3.axhline(y=chart_config['delta_pc_bullish_threshold'], color=colors['primary_blue'],
                       linestyle='--', alpha=chart_config['line_alpha'], label='Bullish Threshold')
            ax3.axhline(y=chart_config['delta_pc_bearish_threshold'], color=colors['primary_red'],
                       linestyle='--', alpha=chart_config['line_alpha'], label='Bearish Threshold')
            ax3.set_title('Delta Put/Call Ratio (Raw Options Data)')
            ax3.set_ylabel('Delta P/C Ratio')
            ax3.legend()
            ax3.grid(True, alpha=chart_config['grid_alpha'])
        else:
            ax3.text(0.5, 0.5, 'No P/C Ratio Data Available', ha='center', va='center',
                    transform=ax3.transAxes, fontsize=12,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.7))
            ax3.set_title('Delta Put/Call Ratio - No Data')
            ax3.axis('off')
        
        # Chart 4: Volume vs Extreme Events (recent data)
        ax4 = axes[1, 1]
        # Use configuration constants for color mapping
        major_threshold = chart_config['major_convergence_threshold']
        convergence_threshold = chart_config['convergence_threshold']
        scatter_colors = [colors['primary_red'] if score >= major_threshold
                         else colors['primary_orange'] if score >= convergence_threshold
                         else colors['primary_blue']
                         for score in recent_data['extreme_score']]
        ax4.scatter(recent_data['Volume'], recent_data['Total_OI_K'], c=scatter_colors,
                   alpha=chart_config['scatter_alpha'], s=chart_config['scatter_size'])
        ax4.set_title('Volume vs Open Interest (colored by extreme score)')
        ax4.set_xlabel('Volume')
        ax4.set_ylabel('Open Interest (K)')
        ax4.grid(True, alpha=chart_config['grid_alpha'])

        # Format x-axis for time series using configuration constants
        for ax in [ax1, ax3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter(chart_config['date_format']))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=chart_config['date_interval_weekly']))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=chart_config['rotation_angle'])

        # Update title based on convergence event type
        if len(convergence_events) > 0 and 'Expiry Date' in convergence_events.columns:
            plt.suptitle('Forward Convergence Events Analysis (Future Expiry Dates)',
                        fontsize=chart_config['font_size_title'], fontweight='bold')
        else:
            plt.suptitle('Convergence Events Analysis',
                        fontsize=chart_config['font_size_title'], fontweight='bold')

        plt.tight_layout()

        # Save chart using configuration constants
        chart_path = self.config.get_output_path(chart_config['chart_files']['convergence_analysis'])
        plt.savefig(chart_path, dpi=chart_config['dpi'], bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def create_correlation_heatmap(self) -> str:
        """Create correlation heatmap for Greeks and price"""
        if 'correlations' not in self.analysis_results:
            return None

        correlation_matrix = self.analysis_results['correlations']['correlation_matrix']

        # Check if correlation matrix has valid data (not all NaN)
        if correlation_matrix.isna().all().all():
            print("Warning: Correlation matrix contains only NaN values (insufficient data points)")
            return self._create_insufficient_data_chart("correlation_heatmap",
                "Correlation Heatmap",
                "Insufficient data points for correlation analysis.\nNeed multiple time periods to calculate correlations.")

        # Select key variables for heatmap using dynamic column name
        ticker = self.config.TICKER.lower()
        close_col = f'{ticker}_close'
        key_vars = [close_col, 'Vomma_M', 'Vanna_K', 'Charm_M', 'Theta_M',
                   'Delta_M', 'Gamma_M', 'Vega_M', 'Total_OI_K', 'Volume', 'Delta_PC_Ratio']

        # Filter correlation matrix and check for available columns
        available_vars = [var for var in key_vars if var in correlation_matrix.columns]
        if len(available_vars) < 3:
            print(f"Warning: Only {len(available_vars)} variables available for correlation analysis")
            return self._create_insufficient_data_chart("correlation_heatmap",
                "Correlation Heatmap",
                f"Insufficient variables for correlation analysis.\nOnly {len(available_vars)} of {len(key_vars)} variables available.")

        # Filter correlation matrix
        filtered_corr = correlation_matrix.loc[available_vars, available_vars]

        # Replace any remaining NaN values with 0 for visualization
        filtered_corr = filtered_corr.fillna(0)

        fig, ax = plt.subplots(figsize=(12, 10))

        # Create heatmap
        sns.heatmap(filtered_corr, annot=True, cmap='RdBu_r', center=0,
                   square=True, fmt='.3f', cbar_kws={'label': 'Correlation'})

        plt.title(f'{self.config.TICKER} Greeks and Price Correlation Matrix', fontsize=16, fontweight='bold')
        plt.tight_layout()

        # Save chart using configuration constants
        chart_config = self.config.CHART_CONFIG
        chart_path = self.config.get_output_path(chart_config['chart_files']['correlation_heatmap'])
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        self.chart_paths.append(chart_path)
        return str(chart_path)

    def _create_insufficient_data_chart(self, chart_type: str, title: str, message: str) -> str:
        """Create a placeholder chart when there's insufficient data"""
        fig, ax = plt.subplots(figsize=(12, 8))
        ax.text(0.5, 0.5, message, ha='center', va='center', fontsize=14,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.7))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_title(f'{self.config.TICKER} {title}', fontsize=16, fontweight='bold')
        ax.axis('off')

        # Save chart using configuration constants
        chart_config = self.config.CHART_CONFIG
        chart_path = self.config.get_output_path(chart_config['chart_files'][chart_type])
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        self.chart_paths.append(chart_path)
        return str(chart_path)

    def create_regime_analysis_chart(self) -> str:
        """Create market regime analysis chart focused on forward-looking expiry date analysis"""
        if 'regimes' not in self.analysis_results:
            return None

        ticker = self.config.TICKER

        # Check if we have raw data for forward-looking expiry analysis
        raw_data_sample = self.analysis_results.get('raw_data_sample')
        use_expiry_analysis = raw_data_sample is not None and len(raw_data_sample) > 0

        if use_expiry_analysis:
            # Forward-looking expiry date analysis
            today = pd.Timestamp.now().date()
            thirty_days_out = today + pd.Timedelta(days=30)

            # Filter for future expiry dates within next 30 days
            future_data = raw_data_sample[
                (raw_data_sample['Expiry Date'].dt.date > today) &
                (raw_data_sample['Expiry Date'].dt.date <= thirty_days_out)
            ].copy()

            if len(future_data) > 0:
                # Group by expiry date to analyze regime patterns
                expiry_analysis = future_data.groupby('Expiry Date').agg({
                    'Vomma': ['sum', 'std'], 'Vanna': ['sum', 'std'], 'Charm': ['sum', 'std'],
                    'Theta': ['sum', 'std'], 'Open Interest': ['sum', 'mean'],
                    'Strike': ['min', 'max', 'count'], f'{self.config.TICKER.lower()}_close': 'first', 'DTE': 'first'
                }).reset_index()

                # Flatten column names
                # Dynamic column naming for ticker-specific close price
                ticker_close_col = f'{self.config.TICKER}_Close'
                expiry_analysis.columns = ['Expiry_Date', 'Vomma_Sum', 'Vomma_Std', 'Vanna_Sum', 'Vanna_Std',
                                         'Charm_Sum', 'Charm_Std', 'Theta_Sum', 'Theta_Std', 'OI_Sum', 'OI_Mean',
                                         'Strike_Min', 'Strike_Max', 'Strike_Count', ticker_close_col, 'DTE']

                # Calculate regime classifications for future expiry dates
                expiry_analysis['volatility_regime'] = self._classify_expiry_volatility_regime(expiry_analysis)
                expiry_analysis['greek_regime'] = self._classify_expiry_greek_regime(expiry_analysis)

                recent_regime_data = expiry_analysis
                start_date = recent_regime_data['Expiry_Date'].min().date()
                end_date = recent_regime_data['Expiry_Date'].max().date()
                print(f"Regime chart: analyzing {len(recent_regime_data)} future expiry dates ({start_date} to {end_date})")
            else:
                use_expiry_analysis = False

        if not use_expiry_analysis:
            # Fall back to historical daily data
            regime_data = self.analysis_results['regimes']['regime_data']
            recent_regime_data = regime_data.tail(30).copy()

            if len(recent_regime_data) == 0:
                recent_regime_data = regime_data.copy()

            # Check if we have sufficient data for meaningful regime analysis
            if len(recent_regime_data) < 2:
                print(f"Warning: Only {len(recent_regime_data)} data point(s) available for regime analysis")
                return self._create_insufficient_data_chart("regime_analysis",
                    "Market Regime Analysis",
                    f"Insufficient data for regime analysis.\nOnly {len(recent_regime_data)} data point(s) available.\nNeed multiple time periods for meaningful regime classification.")

            start_date = recent_regime_data['date'].min().date()
            end_date = recent_regime_data['date'].max().date()
            print(f"Regime chart: showing last {len(recent_regime_data)} data points ({start_date} to {end_date})")

        fig, axes = plt.subplots(2, 1, figsize=(15, 10))

        if use_expiry_analysis:
            fig.suptitle('📊 Forward-Looking Market Regime Analysis - Future Expiry Dates 📊', fontsize=16, fontweight='bold')
        else:
            fig.suptitle('📊 Current Market Regime Analysis - Recent Trends 📊', fontsize=16, fontweight='bold')

        # Chart 1: Price with Greek regimes
        ax1 = axes[0]

        # Color code by Greek regime
        regime_colors = {'normal': 'blue', 'extreme': 'red', 'distribution': 'orange', 'collapse': 'purple'}

        if use_expiry_analysis:
            # Forward-looking expiry analysis
            for regime in recent_regime_data['greek_regime'].unique():
                regime_subset = recent_regime_data[recent_regime_data['greek_regime'] == regime]
                # Use dynamic column name for price
                ticker_close_col = f'{self.config.TICKER}_Close'
                ax1.scatter(regime_subset['Expiry_Date'], regime_subset[ticker_close_col],
                           c=regime_colors.get(regime, 'gray'), label=f'{regime.title()} Regime',
                           alpha=0.7, s=80)

            # Add current price as horizontal line
            current_price = recent_regime_data[ticker_close_col].iloc[0]
            ax1.axhline(y=current_price, color='black', linestyle='-', alpha=0.8, linewidth=2, label=f'Current {self.config.TICKER}: ${current_price:.0f}')

            ax1.set_xlabel('Future Expiry Date')
            ax1.set_ylabel('SPX Price Level')
            ax1.set_title('Greek Regime Classification by Future Expiry Date')
        else:
            # Historical daily analysis
            for regime in recent_regime_data['greek_regime'].unique():
                regime_subset = recent_regime_data[recent_regime_data['greek_regime'] == regime]
                # Use dynamic column name for price (lowercase for daily data)
                ticker_close_col_lower = f'{self.config.TICKER.lower()}_close'
                ax1.scatter(regime_subset['date'], regime_subset[ticker_close_col_lower],
                           c=regime_colors.get(regime, 'gray'), label=f'{regime.title()} Regime',
                           alpha=0.7, s=50)

            ax1.set_xlabel('Date')
            ax1.set_ylabel('SPX Price')
            ax1.set_title('Recent Greek Regime Classification')

        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Chart 2: Volatility regimes
        ax2 = axes[1]

        vol_colors = {'low_vol': 'green', 'normal': 'blue', 'high_vol': 'red'}

        if use_expiry_analysis:
            # Forward-looking expiry analysis
            for regime in recent_regime_data['volatility_regime'].unique():
                regime_subset = recent_regime_data[recent_regime_data['volatility_regime'] == regime]
                # Use dynamic column name for price
                ticker_close_col = f'{self.config.TICKER}_Close'
                ax2.scatter(regime_subset['Expiry_Date'], regime_subset[ticker_close_col],
                           c=vol_colors.get(regime, 'gray'), label=f'{regime.replace("_", " ").title()}',
                           alpha=0.7, s=80)

            # Add current price as horizontal line
            current_price = recent_regime_data[ticker_close_col].iloc[0]
            ax2.axhline(y=current_price, color='black', linestyle='-', alpha=0.8, linewidth=2, label=f'Current {self.config.TICKER}: ${current_price:.0f}')

            ax2.set_title('Volatility Regime Classification by Future Expiry Date')
            ax2.set_xlabel('Future Expiry Date')
        else:
            # Historical daily analysis
            for regime in recent_regime_data['volatility_regime'].unique():
                regime_subset = recent_regime_data[recent_regime_data['volatility_regime'] == regime]
                # Use dynamic column name for price (lowercase for daily data)
                ticker_close_col_lower = f'{self.config.TICKER.lower()}_close'
                ax2.scatter(regime_subset['date'], regime_subset[ticker_close_col_lower],
                           c=vol_colors.get(regime, 'gray'), label=f'{regime.replace("_", " ").title()}',
                           alpha=0.7, s=50)

            # Add trend line for recent price movement
            ax2.plot(recent_regime_data['date'], recent_regime_data[ticker_close_col_lower],
                    color='black', alpha=0.3, linewidth=1, label='Price Trend')

            ax2.set_title(f'Recent {ticker} Price by Volatility Regime (Current Market State)')
            ax2.set_xlabel('Date')

        ax2.set_ylabel(f'{ticker} Price')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Format x-axis for data view
        for ax in axes:
            if use_expiry_analysis:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d/%Y'))  # Include year for expiry dates
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=3))  # Every 3 days for expiry dates
            else:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d/%Y'))  # Include year for clarity
                ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))  # Weekly intervals for recent data
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # Add regime status as subtitle
        if len(recent_regime_data) > 0:
            if use_expiry_analysis:
                # Show forward-looking regime summary
                greek_regimes = recent_regime_data['greek_regime'].value_counts()
                vol_regimes = recent_regime_data['volatility_regime'].value_counts()
                regime_text = f"Forward Outlook: {len(recent_regime_data)} expiry dates analyzed | "
                regime_text += f"Greek Regimes: {', '.join([f'{k}({v})' for k, v in greek_regimes.head(2).items()])} | "
                regime_text += f"Vol Regimes: {', '.join([f'{k}({v})' for k, v in vol_regimes.head(2).items()])}"
            else:
                # Show current regime status
                current_regime = recent_regime_data.iloc[-1]
                regime_text = f'Current State: {current_regime["greek_regime"].title()} Greek Regime, {current_regime["volatility_regime"].replace("_", " ").title()} Volatility'

            fig.text(0.5, 0.93, regime_text,
                    ha='center', fontsize=12, style='italic', color='red', weight='bold')
        
        plt.tight_layout()
        
        # Save chart using configuration constants
        chart_config = self.config.CHART_CONFIG
        chart_path = self.config.get_output_path(chart_config['chart_files']['regime_analysis'])
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def generate_markdown_report(self) -> str:
        """Generate comprehensive markdown report"""
        report_config = self.config.REPORT_CONFIG
        report_path = self.config.get_output_path(report_config['markdown_report'])
        
        # Start with title and summary
        ticker = self.config.TICKER
        with open(report_path, 'w') as f:
            f.write(f"# {ticker} Options Greeks Analysis Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**Analysis Period:** {self.data['date'].min().strftime('%Y-%m-%d')} to {self.data['date'].max().strftime('%Y-%m-%d')}\n\n")
            f.write(f"**Total Days Analyzed:** {len(self.data)}\n\n")
            f.write("---\n\n")
        
        # Executive Summary
        self._append_executive_summary(report_path)
        
        # Key Findings
        self._append_key_findings(report_path)
        
        # Convergence Events Analysis
        self._append_convergence_analysis(report_path)
        
        # Vomma/Price Divergence Analysis
        self._append_divergence_analysis(report_path)
        
        # Market Regime Analysis
        self._append_regime_analysis(report_path)
        
        # Charts section
        self._append_charts_section(report_path)
        
        # Technical Details
        self._append_technical_details(report_path)
        
        return str(report_path)
    
    def _append_executive_summary(self, report_path: str):
        """Append executive summary section with ChatGPT narrative"""
        summary = self.analysis_results.get('summary', {})
        key_findings = summary.get('key_findings', {})

        # Use fallback narrative instead of ChatGPT due to data accuracy issues
        print("Using accurate fallback executive summary (ChatGPT disabled due to data inconsistencies)")
        content = self._fallback_executive_summary(key_findings)

        with open(report_path, 'a') as f:
            f.write(content)

    def _fallback_executive_summary(self, key_findings: dict) -> str:
        """Accurate executive summary using actual analysis results"""
        # Get actual correlation data
        correlations = self.analysis_results.get('correlations', {})
        key_correlations = correlations.get('key_correlations', {})
        vomma_price_corr = key_correlations.get('vomma_price', 0)

        # Get actual convergence events count
        convergence_events = self.analysis_results.get('convergence_events', [])
        convergence_count = len(convergence_events) if hasattr(convergence_events, '__len__') else 0

        # Get distribution signals count
        vomma_div = self.analysis_results.get('vomma_divergence', {})
        distribution_count = len(vomma_div.get('distribution_signals', []))

        return f"""## Executive Summary

This analysis examines {self.config.TICKER} options Greeks patterns focusing on forward-looking expiry dates and convergence events that indicate institutional positioning patterns.

**Key Findings:**
Our analysis identified **{convergence_count} forward convergence events** where multiple Greeks (Vomma, Vanna, Charm) reach extreme levels simultaneously at future expiry dates. These events suggest significant institutional positioning and potential market volatility around those dates.

The **Vomma-Price correlation of {vomma_price_corr:.3f}** indicates {'strong negative correlation' if vomma_price_corr < -0.5 else 'moderate correlation' if abs(vomma_price_corr) > 0.3 else 'weak correlation'} between volatility risk and price movements. With **{distribution_count} distribution signals** detected, the analysis reveals patterns of institutional flow that may precede significant price movements.

**Actionable Insights:**
Traders should monitor the identified convergence dates for potential volatility expansion. The forward-looking analysis provides early warning signals for institutional positioning changes.

### Key Metrics:
- **Total Convergence Events:** {convergence_count}
- **Distribution Signals:** {distribution_count}
- **OI Collapse Events (Institutional Unwinding):** {key_findings.get('volume_collapse_events', 0)}
- **Extreme Greek Days:** {key_findings.get('extreme_greek_days', 0)}

"""
    
    def _append_key_findings(self, report_path: str):
        """Append key findings section"""
        content = """## Key Findings

### 1. Convergence Events
"""
        
        if 'convergence_events' in self.analysis_results:
            convergence_events = self.analysis_results['convergence_events']
            if len(convergence_events) > 0:
                content += f"Identified {len(convergence_events)} convergence events where multiple Greeks reached extreme levels simultaneously:\n\n"
                
                for _, event in convergence_events.iterrows():
                    # Handle different date columns for forward vs historical convergence
                    event_date = event.get('Expiry Date', event.get('date', pd.Timestamp.now()))
                    # Get price using dynamic column name
                    ticker = self.config.TICKER.lower()
                    close_col = f'{ticker}_close'
                    # Try multiple column name formats for compatibility
                    price = event.get(close_col, event.get(f'{self.config.TICKER.lower()}_close', event.get('spx_close', 0)))

                    content += f"- **{event_date.strftime('%Y-%m-%d')}**: {event['event_type'].replace('_', ' ').title()} "
                    content += f"(Score: {event['extreme_score']}, {self.config.TICKER}: {price:.2f})\n"
                
                content += "\n"
            else:
                content += "No significant convergence events detected in this period.\n\n"
        
        content += """### 2. Vomma/Price Divergence Patterns
"""
        
        if 'vomma_divergence' in self.analysis_results:
            divergence = self.analysis_results['vomma_divergence']
            content += f"- Distribution signals detected: {len(divergence['distribution_signals'])}\n"
            content += f"- Acceleration signals detected: {len(divergence['acceleration_signals'])}\n"
            content += f"- Vomma-Price correlation: {divergence['correlation_vomma_price']:.3f}\n\n"
        
        content += """### 3. Market Regime Classification
"""
        
        if 'regimes' in self.analysis_results:
            regimes = self.analysis_results['regimes']
            content += "**Volatility Regimes:**\n"
            for regime, count in regimes['volatility_regimes'].items():
                content += f"- {regime.replace('_', ' ').title()}: {count} days\n"
            
            content += "\n**Greek Regimes:**\n"
            for regime, count in regimes['greek_regimes'].items():
                content += f"- {regime.replace('_', ' ').title()}: {count} days\n"
            content += "\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_convergence_analysis(self, report_path: str):
        """Append detailed convergence analysis with ChatGPT narrative"""
        # Get data-driven thresholds for accurate reporting
        thresholds = self.analysis_results.get('convergence_thresholds', {})
        vomma_threshold = thresholds.get('vomma_threshold', 0)
        vanna_threshold = thresholds.get('vanna_threshold', 0)

        content = f"""## Convergence Events Analysis

Convergence events occur when multiple Greeks reach extreme levels simultaneously, often indicating institutional positioning or market engineering. This analysis uses data-driven thresholds based on statistical percentiles rather than arbitrary values.

### Data-Driven Scoring System:
- **Extreme Vomma (>{vomma_threshold:.1f}M):** +{self.config.DATA_DRIVEN_CONFIG['vomma_score_weight']} points
- **Extreme Vanna (>{vanna_threshold:.1f}K):** +{self.config.DATA_DRIVEN_CONFIG['vanna_score_weight']} points
- **Extreme Charm:** +{self.config.DATA_DRIVEN_CONFIG['charm_score_weight']} points
- **Extreme Theta:** +{self.config.DATA_DRIVEN_CONFIG['theta_score_weight']} points
- **High Open Interest:** +{self.config.DATA_DRIVEN_CONFIG['oi_score_weight']} point
- **Extreme Delta P/C Ratio:** +{self.config.DATA_DRIVEN_CONFIG['delta_pc_score_weight']} point

### Event Classifications:
- **Convergence (Score {self.config.DATA_DRIVEN_CONFIG['convergence_min_score']}-{self.config.DATA_DRIVEN_CONFIG['major_convergence_min_score']-1}):** Multiple extremes present
- **Major Convergence (Score {self.config.DATA_DRIVEN_CONFIG['major_convergence_min_score']}-{self.config.DATA_DRIVEN_CONFIG['historic_convergence_min_score']-1}):** Significant institutional activity
- **Historic Convergence (Score {self.config.DATA_DRIVEN_CONFIG['historic_convergence_min_score']}+):** Unprecedented positioning

"""

        # Use accurate data instead of ChatGPT narrative
        print("Using accurate convergence analysis (ChatGPT disabled for data consistency)")

        # Add accurate convergence analysis
        convergence_events = self.analysis_results.get('convergence_events', [])
        if hasattr(convergence_events, '__len__') and len(convergence_events) > 0:
            content += f"""### Market Analysis:

The {len(convergence_events)} forward convergence events identified represent periods where multiple Greeks reach extreme levels simultaneously at future expiry dates. These patterns typically indicate:

- **Institutional Positioning**: Large-scale options strategies being deployed
- **Volatility Preparation**: Market makers hedging for expected volatility
- **Price Inflection Points**: Potential areas of significant price movement

The convergence events span from July to October 2025, suggesting sustained institutional activity across multiple expiry cycles.

"""

        if 'convergence_events' in self.analysis_results:
            convergence_events = self.analysis_results['convergence_events']
            if len(convergence_events) > 0:
                content += "### Detailed Event Analysis:\n\n"

                for _, event in convergence_events.iterrows():
                    content += f"#### {event['Expiry Date'].strftime('%Y-%m-%d')} - {event['event_type'].replace('_', ' ').title()}\n"
                    content += f"- **Extreme Score:** {event['extreme_score']}\n"
                    # Use dynamic column name for price
                    ticker_close_col = f'{self.config.TICKER.lower()}_close'
                    price = event.get(ticker_close_col, event.get('spx_close', 0))  # Fallback for compatibility
                    content += f"- **{self.config.TICKER} Price:** {price:.2f}\n"

                    # Handle different column structures for forward vs historical convergence
                    if 'Vomma_Sum' in event.index:  # Forward convergence events
                        content += f"- **Vomma (Total):** {event['Vomma_Sum']:.0f}\n"
                        content += f"- **Vanna (Total):** {event['Vanna_Sum']:.0f}\n"
                        content += f"- **Open Interest (Total):** {event['OI_Sum']:.0f}\n"
                        content += f"- **Strike Range:** {event['Strike_Min']:.0f} - {event['Strike_Max']:.0f}\n"
                        content += f"- **Strike Count:** {event['Strike_Count']}\n"
                        content += f"- **Days to Expiry:** {event['DTE']}\n"
                    else:  # Historical convergence events
                        content += f"- **Vomma:** {event.get('Vomma_M', 0):.2f}M\n"
                        content += f"- **Vanna:** {event.get('Vanna_K', 0):.2f}K\n"
                        content += f"- **Open Interest:** {event.get('Total_OI_K', 0):.2f}K\n"
                        if 'Delta_PC_Ratio' in event.index:
                            content += f"- **Delta P/C Ratio:** {event['Delta_PC_Ratio']:.3f}\n"

                    content += "\n"

        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_divergence_analysis(self, report_path: str):
        """Append Vomma/Price divergence analysis"""
        content = """## Vomma/Price Divergence Analysis

Vomma (volatility of volatility) divergence from price movements can signal distribution or acceleration patterns:

### Distribution Pattern:
- Price rises while Vomma explodes higher
- Indicates institutional distribution into retail buying
- Creates perfect trap for late buyers

### Acceleration Pattern:  
- Price falls with negative Vomma
- Creates self-reinforcing decline
- Amplifies volatility impact

"""
        
        if 'vomma_divergence' in self.analysis_results:
            divergence = self.analysis_results['vomma_divergence']
            
            content += f"### Analysis Results:\n"
            content += f"- **Vomma-Price Correlation:** {divergence['correlation_vomma_price']:.3f}\n"
            content += f"- **Distribution Signals:** {len(divergence['distribution_signals'])}\n"
            content += f"- **Acceleration Signals:** {len(divergence['acceleration_signals'])}\n\n"
            
            if len(divergence['distribution_signals']) > 0:
                content += "### Distribution Signal Details:\n\n"
                for _, signal in divergence['distribution_signals'].head(3).iterrows():
                    content += f"- **{signal['date'].strftime('%Y-%m-%d')}**: Price +{signal['price_change']:.2f}, "
                    content += f"Vomma {signal['Vomma_M']:.2f}M (+{signal['vomma_change']:.2f}M)\n"
                content += "\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_regime_analysis(self, report_path: str):
        """Append market regime analysis with ChatGPT narrative"""
        content = """## Market Regime Analysis

Market regimes are classified based on volatility levels and Greek extremes using data-driven thresholds:

### Volatility Regimes:
- **Low Vol:** Bottom 20% of Vega readings
- **Normal:** Middle 60% of Vega readings
- **High Vol:** Top 20% of Vega readings

### Greek Regimes:
- **Normal:** Standard Greek levels
- **Extreme:** Multiple Greeks at extreme levels (85th percentile)
- **Distribution:** Price up + high Greeks (75th percentile)
- **Collapse:** Low Vomma + price down (25th percentile)

"""

        if 'regimes' in self.analysis_results:
            regimes = self.analysis_results['regimes']

            content += "### Regime Distribution:\n\n"
            content += "**Volatility Regimes:**\n"
            total_days = sum(regimes['volatility_regimes'].values())
            for regime, count in regimes['volatility_regimes'].items():
                pct = (count / total_days) * 100
                content += f"- {regime.replace('_', ' ').title()}: {count} days ({pct:.1f}%)\n"

            content += "\n**Greek Regimes:**\n"
            total_days = sum(regimes['greek_regimes'].values())
            for regime, count in regimes['greek_regimes'].items():
                pct = (count / total_days) * 100
                content += f"- {regime.replace('_', ' ').title()}: {count} days ({pct:.1f}%)\n"
            content += "\n"

            # Add ChatGPT narrative
            if self.narrative_generator:
                try:
                    print("Generating regime analysis narrative with ChatGPT...")
                    narrative = self.narrative_generator.generate_regime_narrative(self.analysis_results)
                    content += f"### Market Interpretation:\n\n{narrative}\n\n"
                except Exception as e:
                    print(f"Error generating regime narrative: {e}")

        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_charts_section(self, report_path: str):
        """Append charts section"""
        content = """## Charts and Visualizations

The following charts provide visual analysis of the Greeks patterns and market behavior:

"""
        
        # Use chart descriptions from configuration
        chart_descriptions = self.config.CHART_CONFIG['chart_descriptions']
        
        for chart_path in self.chart_paths:
            chart_name = Path(chart_path).name
            if chart_name in chart_descriptions:
                content += f"### {chart_descriptions[chart_name]}\n\n"
                content += f"![{chart_descriptions[chart_name]}]({chart_name})\n\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_technical_details(self, report_path: str):
        """Append technical details section"""
        content = """## Chart Types: Historical vs Future Projection

**Two Analysis Perspectives:**

### 1. Historical Trading Analysis (Time Series Charts)
- **X-Axis**: Trading dates when options data was recorded (April-June 2025)
- **Purpose**: Shows how Greeks evolved during the trading period
- **Use Case**: Understanding past market behavior and patterns

### 2. Future Price Projection Analysis (Expiry-Focused Charts)
- **X-Axis**: Option expiry dates (future dates beyond analysis period)
- **Purpose**: Projects potential price impact based on option expirations
- **Use Case**: Forecasting future price movements and volatility events

**Key Insight**: The "Future Price Projection" chart groups all options by their expiry dates to show when Greeks exposure will impact the market, regardless of when the options were traded.

**Filtering Applied**: Only options with expiry dates AFTER the analysis date are included (no expired options).

## Technical Details

### Data Processing:
- **Source:** SPX options chain data Q2 2025
- **Greeks Calculated:** Delta, Gamma, Vega, Theta, Rho
- **Derived Greeks:** Vomma, Vanna, Charm
- **Aggregation:** Daily totals weighted by Open Interest

### Methodology:
- **Vomma Calculation:** Second derivative of option value with respect to volatility (Vega × d1 × d2 / σ)
- **Vanna Calculation:** Cross-sensitivity between Delta and volatility (-e^(-qT) × φ(d1) × d2 / σ)
- **Charm Calculation:** Rate of change of Delta with respect to time (complex Black-Scholes time decay formula)
- **All Greeks:** Calculated using precise Black-Scholes mathematical formulas, not approximations
- **Extreme Thresholds:** Based on statistical analysis of calculated Greeks data

### Black-Scholes Implementation Details:
**Primary Greeks:**
- **Delta:** ∂V/∂S (price sensitivity)
- **Gamma:** ∂²V/∂S² (Delta sensitivity)
- **Vega:** ∂V/∂σ (volatility sensitivity)
- **Theta:** ∂V/∂t (time decay)
- **Rho:** ∂V/∂r (interest rate sensitivity)

**Advanced Greeks (Second & Cross Derivatives):**
- **Vomma:** ∂²V/∂σ² = Vega × d1 × d2 / σ (volatility convexity)
- **Vanna:** ∂²V/∂S∂σ = -e^(-qT) × φ(d1) × d2 / σ (price-volatility cross-sensitivity)
- **Charm:** ∂²V/∂S∂t (Delta time decay with complex Black-Scholes time components)

**Parameters Used:**
- Risk-free rate: 5.0%
- Dividend yield: 1.5% (SPX)
- Volatility: Implied volatility from options data
- Time to expiration: Calculated from expiry dates

### Risk Considerations:
- Analysis based on historical data patterns
- Market conditions can change rapidly
- Greeks calculations are approximations
- Past patterns may not predict future behavior

---

*This report was generated using the {self.config.TICKER} Greeks Analysis Engine*
"""
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def create_expiry_focused_analysis_chart(self) -> str:
        """Create future-focused analysis chart grouped by expiry dates for price projection"""
        # We need access to the raw processed data to analyze by expiry dates
        if 'raw_data_sample' not in self.analysis_results:
            return None

        raw_data = self.analysis_results['raw_data_sample']

        # Filter for FUTURE expiry dates only using configuration constants
        chart_config = self.config.CHART_CONFIG
        future_days = chart_config['future_analysis_days']  # 30 days from config

        today = pd.Timestamp.now().date()
        thirty_days_out = today + pd.Timedelta(days=future_days)

        future_data = raw_data[
            (raw_data['Expiry Date'].dt.date > today) &
            (raw_data['Expiry Date'].dt.date <= thirty_days_out)
        ].copy()

        if len(future_data) == 0:
            print(f"Warning: No expiry dates found in next 30 days ({today} to {thirty_days_out})")
            return None

        # Group data by expiry date and aggregate Greeks using dynamic column names
        ticker = self.config.TICKER.lower()
        close_col = f'{ticker}_close'

        agg_dict = {
            'Vomma': 'sum',
            'Vanna': 'sum',
            'Charm': 'sum',
            'Theta': 'sum',
            'Open Interest': 'sum',
            'Strike': 'mean',  # Average strike for reference
            'DTE': 'first'  # Days to expiration
        }

        # Add close price column if it exists
        if close_col in future_data.columns:
            agg_dict[close_col] = 'first'

        expiry_aggregates = future_data.groupby('Expiry Date').agg(agg_dict).reset_index()

        # Convert to same scale as daily aggregates using configuration constants
        million_scale = chart_config['million_scale']
        thousand_scale = chart_config['thousand_scale']

        expiry_aggregates['Vomma_M'] = expiry_aggregates['Vomma'] / million_scale
        expiry_aggregates['Vanna_K'] = expiry_aggregates['Vanna'] / thousand_scale
        expiry_aggregates['Charm_M'] = expiry_aggregates['Charm'] / million_scale
        expiry_aggregates['Theta_M'] = expiry_aggregates['Theta'] / million_scale
        expiry_aggregates['Total_OI_K'] = expiry_aggregates['Open Interest'] / thousand_scale

        # Sort by expiry date for future projection
        expiry_aggregates = expiry_aggregates.sort_values('Expiry Date')

        # Calculate projected price impact based on Greeks using configuration constants
        # Get current price using dynamic column name
        ticker_close_col = f'{self.config.TICKER.lower()}_close'
        current_spx = expiry_aggregates[ticker_close_col].iloc[0]  # Current price level
        impact_factors = chart_config['projection_impact_factors']

        # Calculate base impact (convert to meaningful SPX price points)
        vomma_impact = expiry_aggregates['Vomma_M'] * impact_factors['vomma_factor']
        vanna_impact = expiry_aggregates['Vanna_K'] * impact_factors['vanna_factor']
        charm_impact = expiry_aggregates['Charm_M'] * impact_factors['charm_factor']

        expiry_aggregates['Projected_Impact'] = vomma_impact + vanna_impact + charm_impact

        # Calculate EXPIRY-SPECIFIC IMPLIED VOLATILITY for each expiry date
        # IV changes by expiry date - create dynamic volatility term structure
        if 'raw_data_sample' in self.analysis_results:
            raw_sample = self.analysis_results['raw_data_sample']
        else:
            raw_sample = pd.DataFrame()  # Empty fallback

        # Calculate IV by expiry date for dynamic volatility term structure
        expiry_iv_data = {}

        if len(raw_sample) > 0:
            from scipy.optimize import minimize_scalar
            import numpy as np

            def black_scholes_call_price(S, K, T, r, sigma):
                """Black-Scholes call option price"""
                from scipy.stats import norm
                if T <= 0 or sigma <= 0:
                    return max(S - K, 0)  # Intrinsic value
                d1 = (np.log(S/K) + (r + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
                d2 = d1 - sigma*np.sqrt(T)
                return S*norm.cdf(d1) - K*np.exp(-r*T)*norm.cdf(d2)

            def implied_vol_objective(sigma, S, K, T, r, market_price):
                """Objective function for IV calculation"""
                try:
                    bs_price = black_scholes_call_price(S, K, T, r, sigma)
                    return abs(bs_price - market_price)
                except:
                    return 1e6

            # Group by expiry date and calculate IV for each
            risk_free_rate = 0.05  # 5% risk-free rate

            for expiry_date in expiry_aggregates['Expiry Date']:
                expiry_options = raw_sample[
                    (raw_sample['Expiry Date'].dt.date == expiry_date.date()) &
                    (abs(raw_sample['Strike'] - current_spx) <= 100) &  # ATM ±100 points
                    (raw_sample['Call/Put'] == 'Call')  # Use calls for IV calculation
                ].copy()

                if len(expiry_options) > 0:
                    iv_estimates = []

                    for _, option in expiry_options.head(20).iterrows():  # Sample ATM options
                        if option.get('Last', 0) > 0.5:  # Valid market price > $0.50
                            T = option['DTE'] / 365.0  # Time to expiration in years
                            market_price = option['Last']

                            if T > 0.01:  # At least 3-4 days to expiry
                                # Solve for implied volatility
                                result = minimize_scalar(
                                    implied_vol_objective,
                                    bounds=(0.05, 1.5),  # 5% to 150% volatility
                                    method='bounded',
                                    args=(current_spx, option['Strike'], T, risk_free_rate, market_price)
                                )

                                if result.success and 0.08 <= result.x <= 0.8:  # Reasonable IV range
                                    iv_estimates.append(result.x)

                    if iv_estimates:
                        expiry_iv = np.mean(iv_estimates)
                        expiry_iv_std = np.std(iv_estimates) if len(iv_estimates) > 1 else 0.05
                        expiry_iv_data[expiry_date] = {
                            'iv': expiry_iv,
                            'iv_std': expiry_iv_std,
                            'sample_size': len(iv_estimates)
                        }

                        print(f"Expiry {expiry_date}: IV={expiry_iv:.1%} (±{expiry_iv_std:.1%}, n={len(iv_estimates)})")
                    else:
                        # Fallback: estimate IV based on DTE
                        if isinstance(expiry_date, pd.Timestamp):
                            dte = (expiry_date.date() - pd.Timestamp.now().date()).days
                        else:
                            dte = (expiry_date - pd.Timestamp.now().date()).days
                        base_iv = 0.20  # 20% base volatility
                        # Shorter-term options typically have higher IV
                        if dte <= 7:
                            estimated_iv = base_iv * 1.3  # +30% for weekly options
                        elif dte <= 30:
                            estimated_iv = base_iv * 1.1  # +10% for monthly options
                        else:
                            estimated_iv = base_iv  # Base IV for longer-term

                        expiry_iv_data[expiry_date] = {
                            'iv': estimated_iv,
                            'iv_std': 0.05,
                            'sample_size': 0
                        }
                        print(f"Expiry {expiry_date}: Estimated IV={estimated_iv:.1%} (DTE={dte})")
                else:
                    # No options data for this expiry - use DTE-based estimate
                    if isinstance(expiry_date, pd.Timestamp):
                        dte = (expiry_date.date() - pd.Timestamp.now().date()).days
                    else:
                        dte = (expiry_date - pd.Timestamp.now().date()).days
                    base_iv = 0.20
                    if dte <= 7:
                        estimated_iv = base_iv * 1.3
                    elif dte <= 30:
                        estimated_iv = base_iv * 1.1
                    else:
                        estimated_iv = base_iv

                    expiry_iv_data[expiry_date] = {
                        'iv': estimated_iv,
                        'iv_std': 0.05,
                        'sample_size': 0
                    }

        # Add expiry-specific IV and price projections to aggregates
        # Note: expiry_aggregates has reset_index(), so 'Expiry Date' is a column, not the index
        for idx, row in expiry_aggregates.iterrows():
            expiry_date = row['Expiry Date']
            if expiry_date in expiry_iv_data:
                iv_info = expiry_iv_data[expiry_date]
                expiry_iv = iv_info['iv']

                # Calculate time to expiry
                if isinstance(expiry_date, pd.Timestamp):
                    dte = (expiry_date.date() - pd.Timestamp.now().date()).days
                else:
                    dte = (expiry_date - pd.Timestamp.now().date()).days
                time_to_expiry = dte / 365.0

                # Calculate expiry-specific price movement
                # 1-sigma move = S * IV * sqrt(T)
                if time_to_expiry > 0:
                    one_sigma_move = current_spx * expiry_iv * np.sqrt(time_to_expiry)
                else:
                    one_sigma_move = 0

                # Store expiry-specific data using row index
                expiry_aggregates.loc[idx, 'Implied_Vol'] = expiry_iv
                expiry_aggregates.loc[idx, 'Vol_Uncertainty'] = one_sigma_move
                expiry_aggregates.loc[idx, 'DTE'] = dte
            else:
                # Fallback values using row index
                expiry_aggregates.loc[idx, 'Implied_Vol'] = 0.20
                expiry_aggregates.loc[idx, 'Vol_Uncertainty'] = current_spx * 0.20 * np.sqrt(30/365.0)
                expiry_aggregates.loc[idx, 'DTE'] = 30

        # Calculate overall volatility statistics
        if expiry_iv_data:
            all_ivs = [info['iv'] for info in expiry_iv_data.values()]
            avg_iv = np.mean(all_ivs)
            iv_range = f"{min(all_ivs):.1%} - {max(all_ivs):.1%}"
            print(f"IV Term Structure: Average={avg_iv:.1%}, Range={iv_range}")

            # Use average IV for overall price uncertainty
            avg_time_horizon = 30 / 365.0  # 30 days
            price_uncertainty = current_spx * avg_iv * np.sqrt(avg_time_horizon)
            projected_spx_center = current_spx
        else:
            # Final fallback
            avg_iv = 0.20
            price_uncertainty = current_spx * avg_iv * np.sqrt(30/365.0)
            projected_spx_center = current_spx
            print(f"Using fallback volatility: {avg_iv:.1%}")

        # Use EXPIRY-SPECIFIC IMPLIED VOLATILITY for dynamic price projections
        # Each expiry date gets its own volatility-based price projection
        expiry_aggregates['Projected_SPX'] = projected_spx_center + expiry_aggregates['Projected_Impact']
        # Create dynamic band column names
        upper_band_col = f'{self.config.TICKER}_Upper_Band'
        lower_band_col = f'{self.config.TICKER}_Lower_Band'
        expiry_aggregates[upper_band_col] = expiry_aggregates['Projected_SPX'] + expiry_aggregates['Vol_Uncertainty']
        expiry_aggregates[lower_band_col] = expiry_aggregates['Projected_SPX'] - expiry_aggregates['Vol_Uncertainty']

        # Display volatility term structure summary
        vol_pct = price_uncertainty / current_spx
        iv_summary = f"IV Range: {expiry_aggregates['Implied_Vol'].min():.1%} - {expiry_aggregates['Implied_Vol'].max():.1%}"
        vol_range = f"±{expiry_aggregates['Vol_Uncertainty'].min():.0f} to ±{expiry_aggregates['Vol_Uncertainty'].max():.0f} points"
        print(f"SPX Price Projection by Expiry: {iv_summary}, Volatility Bands: {vol_range}")

        fig, axes = plt.subplots(4, 1, figsize=(15, 18))
        fig.suptitle(f'📈 NEXT 30 DAYS: Greeks by Option Expiry Date ({today} → {thirty_days_out}) 📈',
                    fontsize=16, fontweight='bold', color='darkblue')

        # Add today's date line for reference
        today_line = pd.Timestamp.now()

        # Chart 1: Price Projection with Bands Based on Greeks
        ax1 = axes[0]
        ax1_twin = ax1.twinx()

        # Current price level using dynamic column name
        ticker_close_col = f'{self.config.TICKER.lower()}_close'
        current_price = expiry_aggregates[ticker_close_col].iloc[0]
        ax1.axhline(y=current_price, color='black', linestyle='--', alpha=0.8, linewidth=2, label=f'Current {self.config.TICKER}: {current_price:.0f}')

        # Plot price projection bands
        line1 = ax1.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Projected_SPX'],
                        color='blue', linewidth=3, marker='o', markersize=8, label='Projected SPX Price')

        # Fill area between EXPIRY-SPECIFIC volatility bands (using dynamic ticker naming)
        upper_band_col = f'{self.config.TICKER}_Upper_Band'
        lower_band_col = f'{self.config.TICKER}_Lower_Band'
        ax1.fill_between(expiry_aggregates['Expiry Date'],
                        expiry_aggregates[lower_band_col],
                        expiry_aggregates[upper_band_col],
                        alpha=0.2, color='blue', label='Expiry-Specific Volatility Bands (±1σ)')

        # Plot band boundaries with expiry-specific volatility
        ax1.plot(expiry_aggregates['Expiry Date'], expiry_aggregates[upper_band_col],
                color='lightblue', linestyle=':', alpha=0.7, label='Upper Volatility Band')
        ax1.plot(expiry_aggregates['Expiry Date'], expiry_aggregates[lower_band_col],
                color='lightblue', linestyle=':', alpha=0.7, label='Lower Volatility Band')

        # Greeks impact on separate scale
        line2 = ax1_twin.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Projected_Impact'],
                             color='orange', linewidth=2, marker='s', markersize=6, alpha=0.8, label='Greeks Impact')

        ticker = self.config.TICKER
        ax1.set_title(f'📈 {ticker} Price Projection with Expiry-Specific IV Bands - Next 30 Days ({today} → {thirty_days_out})', fontweight='bold', color='darkblue')
        ax1.set_ylabel(f'{ticker} Price Level', color='blue', fontweight='bold')
        ax1_twin.set_ylabel('Greeks Impact Factor', color='orange')
        ax1.tick_params(axis='y', labelcolor='blue')
        ax1_twin.tick_params(axis='y', labelcolor='orange')

        # Add vertical line for today
        ax1.axvline(x=today_line, color='red', linestyle=':', linewidth=3, alpha=0.9, label=f'TODAY ({today})')

        # Combine legends from both axes
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax1_twin.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=10)
        ax1.grid(True, alpha=0.3)

        # Chart 2: Vomma and Vanna by Future Expiry Date
        ax2 = axes[1]
        ax2_twin = ax2.twinx()

        line3 = ax2.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Vomma_M'],
                        color='red', linewidth=2, marker='o', label='Vomma (M)')
        line4 = ax2_twin.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Vanna_K'],
                             color='green', linewidth=2, marker='s', label='Vanna (K)')

        ax2.set_title(f'📈 Vomma & Vanna - Next 30 Days ({today} → {thirty_days_out})', color='darkblue')
        ax2.set_ylabel('Vomma (Millions)', color='red')
        ax2_twin.set_ylabel('Vanna (Thousands)', color='green')
        ax2.tick_params(axis='y', labelcolor='red')
        ax2_twin.tick_params(axis='y', labelcolor='green')

        # Add vertical line for today
        ax2.axvline(x=today_line, color='red', linestyle=':', linewidth=3, alpha=0.9, label=f'TODAY ({today})')

        # Combine legends
        lines = line3 + line4
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='upper left')
        ax2.grid(True, alpha=0.3)

        # Chart 3: Theta and Charm by Future Expiry Date
        ax3 = axes[2]
        ax3_twin = ax3.twinx()

        line5 = ax3.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Theta_M'],
                        color='purple', linewidth=2, marker='o', label='Theta (M)')
        line6 = ax3_twin.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Charm_M'],
                             color='brown', linewidth=2, marker='s', label='Charm (M)')

        ax3.set_title(f'📈 Theta & Charm - Next 30 Days ({today} → {thirty_days_out})', color='darkblue')
        ax3.set_ylabel('Theta (Millions)', color='purple')
        ax3_twin.set_ylabel('Charm (Millions)', color='brown')
        ax3.tick_params(axis='y', labelcolor='purple')
        ax3_twin.tick_params(axis='y', labelcolor='brown')

        # Add vertical line for today
        ax3.axvline(x=today_line, color='red', linestyle=':', linewidth=3, alpha=0.9, label=f'TODAY ({today})')

        # Combine legends
        lines = line5 + line6
        labels = [l.get_label() for l in lines]
        ax3.legend(lines, labels, loc='upper left')
        ax3.grid(True, alpha=0.3)

        # Chart 4: Future Open Interest and Days to Expiration
        ax4 = axes[3]
        ax4_twin = ax4.twinx()

        bars = ax4.bar(expiry_aggregates['Expiry Date'], expiry_aggregates['Total_OI_K'],
                      alpha=0.6, color='cyan', label='Open Interest (K)')
        line7 = ax4_twin.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['DTE'],
                             color='orange', linewidth=2, marker='o', label='Days to Expiration')

        ax4.set_title(f'📈 Open Interest & DTE - Next 30 Days ({today} → {thirty_days_out})', color='darkblue')
        ax4.set_ylabel('Open Interest (Thousands)', color='cyan')
        ax4_twin.set_ylabel('Days to Expiration', color='orange')
        ax4.set_xlabel(f'📈 Option Expiry Dates - Next 30 Days ({today} → {thirty_days_out})')
        ax4.tick_params(axis='y', labelcolor='cyan')
        ax4_twin.tick_params(axis='y', labelcolor='orange')

        # Add vertical line for today
        ax4.axvline(x=today_line, color='red', linestyle=':', linewidth=3, alpha=0.9, label=f'TODAY ({today})')

        # Format x-axis for all charts - Daily intervals for 30-day view
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))  # Month/day for 30-day view
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=3))  # Every 3 days for 30-day period
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # Add subtitle explaining 30-day focus
        fig.text(0.5, 0.95, f'📈 NEXT 30 DAYS ANALYSIS - Expiry dates from {today} to {thirty_days_out} 📈',
                ha='center', fontsize=12, style='italic', color='red', weight='bold')

        plt.tight_layout()

        # Save chart using configuration constants
        chart_config = self.config.CHART_CONFIG
        chart_path = self.config.get_output_path(chart_config['chart_files']['expiry_focused'])
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        self.chart_paths.append(chart_path)
        return str(chart_path)

    def create_expiry_distribution_chart(self) -> str:
        """Create chart showing distribution of FUTURE option expiry dates"""
        # We need access to the raw processed data to show expiry dates
        if 'raw_data_sample' not in self.analysis_results:
            return None

        raw_data = self.analysis_results['raw_data_sample']

        # Filter for FUTURE expiry dates only (next 30 days)
        today = pd.Timestamp.now().date()
        thirty_days_out = today + pd.Timedelta(days=30)

        future_data = raw_data[
            (raw_data['Expiry Date'].dt.date > today) &
            (raw_data['Expiry Date'].dt.date <= thirty_days_out)
        ].copy()

        if len(future_data) == 0:
            print(f"Warning: No expiry dates found in next 30 days ({today} to {thirty_days_out})")
            return None

        fig, axes = plt.subplots(2, 1, figsize=(15, 10))
        fig.suptitle(f'📈 NEXT 30 DAYS: Options Expiry Analysis ({today} → {thirty_days_out})',
                    fontsize=16, fontweight='bold', color='darkblue')

        # Chart 1: Future expiry date distribution
        ax1 = axes[0]
        expiry_counts = future_data['Expiry Date'].value_counts().sort_index()
        bars1 = ax1.bar(expiry_counts.index, expiry_counts.values, alpha=0.7, color='skyblue')
        ax1.set_title('Distribution of FUTURE Option Expiry Dates (After Today)', fontweight='bold')
        ax1.set_ylabel('Number of Options')
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)

        # Add vertical line for today
        today_line = pd.Timestamp.now()
        ax1.axvline(x=today_line, color='red', linestyle='--', linewidth=2, alpha=0.8, label='Today')
        ax1.legend()

        # Chart 2: Future days to expiration distribution
        ax2 = axes[1]
        dte_counts = future_data['DTE'].value_counts().sort_index()
        bars2 = ax2.bar(dte_counts.index, dte_counts.values, alpha=0.7, color='lightgreen')
        ax2.set_title('Distribution of FUTURE Days to Expiration (DTE > 0)', fontweight='bold')
        ax2.set_xlabel('Days to Expiration')
        ax2.set_ylabel('Number of Options')
        ax2.grid(True, alpha=0.3)

        # Add text annotations for future focus
        min_expiry = future_data['Expiry Date'].min().date()
        max_expiry = future_data['Expiry Date'].max().date()

        ax1.text(0.02, 0.98, f'Analysis Date: {today}\nEarliest Expiry: {min_expiry}\nLatest Expiry: {max_expiry}',
                transform=ax1.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        # Save chart using configuration constants
        chart_config = self.config.CHART_CONFIG
        chart_path = self.config.get_output_path(chart_config['chart_files']['expiry_distribution'])
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        self.chart_paths.append(chart_path)
        return str(chart_path)

    def generate_pdf_report(self) -> str:
        """Generate PDF report from markdown using weasyprint"""
        # First generate all charts
        print("Generating charts...")
        self.create_price_and_greeks_chart()
        self.create_convergence_analysis_chart()
        self.create_correlation_heatmap()
        self.create_regime_analysis_chart()
        self.create_expiry_focused_analysis_chart()  # New future-focused chart
        self.create_expiry_distribution_chart()

        # Generate markdown report
        print("Generating markdown report...")
        md_path = self.generate_markdown_report()

        # Convert to PDF using weasyprint (more reliable than manus)
        print("Converting to PDF...")
        report_config = self.config.REPORT_CONFIG
        pdf_path = self.config.get_output_path(report_config['pdf_report'])

        try:
            # Use reportlab directly (more reliable on macOS)
            print("Generating PDF with reportlab...")
            return self._generate_pdf_with_reportlab(md_path, pdf_path)

        except ImportError:
            print("reportlab not available, trying weasyprint...")
            try:
                # Fallback to weasyprint
                import weasyprint

                # Read markdown and convert to HTML first
                with open(md_path, 'r') as f:
                    markdown_content = f.read()

                # Simple markdown to HTML conversion for basic formatting
                html_content = self._markdown_to_html(markdown_content)

                # Generate PDF from HTML
                weasyprint.HTML(string=html_content, base_url=str(self.config.REPORTS_DIR)).write_pdf(pdf_path)
                print(f"✓ PDF report generated successfully: {os.path.basename(pdf_path)}")
                return str(pdf_path)

            except Exception as e:
                print(f"Error with weasyprint: {e}")
                print("Returning markdown report instead")
                return str(md_path)

        except Exception as e:
            print(f"Error generating PDF: {e}")
            print("Returning markdown report instead")
            return str(md_path)
    
    def _markdown_to_html(self, markdown_content: str) -> str:
        """Convert basic markdown to HTML for PDF generation"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{self.config.TICKER} Greeks Analysis Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
                h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; }}
                h2 {{ color: #34495e; border-bottom: 1px solid #bdc3c7; }}
                h3 {{ color: #7f8c8d; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                img {{ max-width: 100%; height: auto; margin: 20px 0; }}
                .metric {{ font-weight: bold; color: #e74c3c; }}
            </style>
        </head>
        <body>
        """

        # Basic markdown to HTML conversion
        lines = markdown_content.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                html += f"<h1>{line[2:]}</h1>\n"
            elif line.startswith('## '):
                html += f"<h2>{line[3:]}</h2>\n"
            elif line.startswith('### '):
                html += f"<h3>{line[4:]}</h3>\n"
            elif line.startswith('**') and line.endswith('**'):
                html += f"<p class='metric'>{line[2:-2]}</p>\n"
            elif line.startswith('- '):
                html += f"<li>{line[2:]}</li>\n"
            elif line == '---':
                html += "<hr>\n"
            elif line:
                html += f"<p>{line}</p>\n"
            else:
                html += "<br>\n"

        html += "</body></html>"
        return html

    def _generate_pdf_with_reportlab(self, md_path: str, pdf_path: str) -> str:
        """Enhanced PDF generation using reportlab with chart images"""
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors

        doc = SimpleDocTemplate(str(pdf_path), pagesize=A4,
                               rightMargin=72, leftMargin=72,
                               topMargin=72, bottomMargin=18)
        styles = getSampleStyleSheet()
        story = []

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.darkblue,
            alignment=1  # Center alignment
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.darkblue
        )

        with open(md_path, 'r') as f:
            content = f.read()

        # Add title with dynamic ticker
        story.append(Paragraph(f"📊 {self.config.TICKER} Options Greeks Analysis Report 📊", title_style))
        story.append(Spacer(1, 20))

        # Process content line by line
        lines = content.split('\n')
        i = 0
        while i < len(lines):
            line = lines[i].strip()

            if line:
                if line.startswith('# '):
                    # Main heading
                    story.append(Paragraph(line.replace('#', '').strip(), title_style))
                    story.append(Spacer(1, 12))
                elif line.startswith('## '):
                    # Section heading
                    story.append(Paragraph(line.replace('##', '').strip(), heading_style))
                    story.append(Spacer(1, 8))
                elif line.startswith('### '):
                    # Subsection heading
                    story.append(Paragraph(line.replace('###', '').strip(), styles['Heading3']))
                    story.append(Spacer(1, 6))
                elif line.startswith('![') and '](' in line and line.endswith(')'):
                    # Image reference - embed the actual chart
                    try:
                        # Extract image filename from markdown syntax
                        start = line.find('](') + 2
                        end = line.find(')', start)
                        image_name = line[start:end]

                        # Find the full path to the image
                        image_path = None
                        for chart_path in self.chart_paths:
                            chart_path_str = str(chart_path)  # Convert PosixPath to string
                            if chart_path_str.endswith(image_name):
                                image_path = chart_path_str
                                break

                        if image_path and os.path.exists(image_path):
                            # Add the image to PDF
                            img = Image(image_path, width=6*inch, height=4*inch)
                            story.append(img)
                            story.append(Spacer(1, 12))
                            print(f"✓ Added chart to PDF: {image_name}")
                        else:
                            # Fallback to text if image not found
                            story.append(Paragraph(f"Chart: {image_name}", styles['Normal']))
                            story.append(Spacer(1, 6))
                    except Exception as e:
                        print(f"Error adding image {line}: {e}")
                        story.append(Paragraph(line, styles['Normal']))
                        story.append(Spacer(1, 6))
                else:
                    # Regular text
                    if line.startswith('**') and line.endswith('**'):
                        # Bold text
                        story.append(Paragraph(f"<b>{line[2:-2]}</b>", styles['Normal']))
                    elif line.startswith('- '):
                        # Bullet point
                        story.append(Paragraph(f"• {line[2:]}", styles['Normal']))
                    else:
                        story.append(Paragraph(line, styles['Normal']))
                    story.append(Spacer(1, 6))

            i += 1

        # Add page break before charts section
        if len(self.chart_paths) > 0:
            story.append(PageBreak())
            story.append(Paragraph("📈 Additional Charts and Visualizations 📈", heading_style))
            story.append(Spacer(1, 12))

            # Add any remaining charts that weren't embedded in markdown
            for chart_path in self.chart_paths:
                chart_path_str = str(chart_path)  # Convert PosixPath to string
                chart_name = os.path.basename(chart_path_str)
                if os.path.exists(chart_path_str):
                    story.append(Paragraph(f"Chart: {chart_name}", styles['Heading3']))
                    story.append(Spacer(1, 6))
                    img = Image(chart_path_str, width=6*inch, height=4*inch)
                    story.append(img)
                    story.append(Spacer(1, 12))

        doc.build(story)
        print(f"✓ PDF report generated with reportlab and embedded charts: {os.path.basename(pdf_path)}")
        return str(pdf_path)

    def _classify_expiry_volatility_regime(self, expiry_data: pd.DataFrame) -> pd.Series:
        """Classify volatility regime for future expiry dates based on Greeks concentration"""
        # Use Vomma and Vanna as volatility proxies for future expiry dates
        vomma_threshold = expiry_data['Vomma_Sum'].quantile(0.75)  # Top 25% = high vol
        vanna_threshold = expiry_data['Vanna_Sum'].quantile(0.75)  # Top 25% = high vol

        conditions = [
            (expiry_data['Vomma_Sum'] > vomma_threshold) | (expiry_data['Vanna_Sum'] > vanna_threshold),
            (expiry_data['Vomma_Sum'] < expiry_data['Vomma_Sum'].quantile(0.25)) &
            (expiry_data['Vanna_Sum'] < expiry_data['Vanna_Sum'].quantile(0.25))
        ]
        choices = ['high_vol', 'low_vol']

        return pd.Series(np.select(conditions, choices, default='normal'), index=expiry_data.index)

    def _classify_expiry_greek_regime(self, expiry_data: pd.DataFrame) -> pd.Series:
        """Classify Greek regime for future expiry dates based on Greeks extremes"""
        # Use multiple Greeks to classify regime for future expiry dates
        vomma_extreme = expiry_data['Vomma_Sum'] > expiry_data['Vomma_Sum'].quantile(0.8)
        vanna_extreme = expiry_data['Vanna_Sum'] > expiry_data['Vanna_Sum'].quantile(0.8)
        charm_extreme = abs(expiry_data['Charm_Sum']) > abs(expiry_data['Charm_Sum']).quantile(0.8)
        theta_extreme = abs(expiry_data['Theta_Sum']) > abs(expiry_data['Theta_Sum']).quantile(0.8)

        # High concentration of strikes at expiry = distribution setup
        high_concentration = expiry_data['Strike_Count'] > expiry_data['Strike_Count'].quantile(0.8)

        conditions = [
            vomma_extreme & vanna_extreme & high_concentration,  # Distribution setup
            (vomma_extreme | vanna_extreme | charm_extreme | theta_extreme),  # Extreme Greeks
            (expiry_data['Vomma_Sum'] < expiry_data['Vomma_Sum'].quantile(0.2)) &
            (expiry_data['OI_Sum'] < expiry_data['OI_Sum'].quantile(0.2))  # Low activity = collapse
        ]
        choices = ['distribution', 'extreme', 'collapse']

        return pd.Series(np.select(conditions, choices, default='normal'), index=expiry_data.index)

    def get_chart_paths(self) -> List[str]:
        """Get list of generated chart paths"""
        return self.chart_paths

