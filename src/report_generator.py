"""
Report Generator for SPX Options Greeks Analysis
Creates comprehensive charts and PDF reports
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from src.narrative_generator import NarrativeGenerator

class GreeksReportGenerator:
    """Report generator for Greeks analysis with charts and PDF output"""
    
    def __init__(self, config):
        self.config = config
        self.data = None
        self.analysis_results = None
        self.chart_paths = []

        # Initialize narrative generator
        try:
            self.narrative_generator = NarrativeGenerator()
            print("✓ ChatGPT narrative generation enabled")
        except Exception as e:
            print(f"⚠ ChatGPT narrative generation disabled: {e}")
            self.narrative_generator = None
        
    def set_data(self, daily_data: pd.DataFrame, analysis_results: Dict):
        """Set the data and analysis results for report generation"""
        # Use processed data with extreme scores if available
        if 'processed_data' in analysis_results:
            self.data = analysis_results['processed_data'].copy()
        else:
            self.data = daily_data.copy()
            # Add basic extreme_score if not present
            if 'extreme_score' not in self.data.columns:
                self.data['extreme_score'] = 0
        
        self.data['date'] = pd.to_datetime(self.data['date'])
        self.analysis_results = analysis_results
        
        # Add price_change if not present
        if 'price_change' not in self.data.columns:
            self.data['price_change'] = self.data['spx_close'].diff()
        
        # Set up matplotlib style
        plt.style.use('default')
        sns.set_palette("Set2")
        
    def create_price_and_greeks_chart(self) -> str:
        """Create comprehensive price and Greeks time series chart showing relevant period"""
        # Use configuration constants for chart dimensions and styling
        chart_config = self.config.CHART_CONFIG

        # Check if we have convergence events to determine optimal time window
        convergence_events = self.analysis_results.get('convergence_events', pd.DataFrame())

        if len(convergence_events) > 0:
            # Show data that includes convergence events plus recent context
            earliest_event = convergence_events['date'].min()
            today = pd.Timestamp.now().date()

            # Show from 2 weeks before earliest event to today for full context
            start_date = (earliest_event - pd.Timedelta(days=14)).date()

            # Filter data to show convergence period plus recent trends
            recent_data = self.data[self.data['date'].dt.date >= start_date].copy()

            print(f"Price/Greeks chart: showing data from {start_date} to {today} to include convergence events and recent trends")
        else:
            # If no convergence events, show recent data (last 45 days for better context)
            today = pd.Timestamp.now().date()
            forty_five_days_ago = today - pd.Timedelta(days=45)
            recent_data = self.data[self.data['date'].dt.date >= forty_five_days_ago].copy()

        if len(recent_data) == 0:
            # If no data in range, use last 15 data points
            recent_data = self.data.tail(15).copy()

        fig, axes = plt.subplots(4, 1, figsize=chart_config['figure_size_extra_large'])
        fig.suptitle(f'📈 SPX Price and Greeks - RECENT TRENDS (Last 30 Days) 📈',
                    fontsize=chart_config['font_size_title'], fontweight='bold')

        # Chart 1: SPX Price with key events
        ax1 = axes[0]
        colors = chart_config['colors']

        ax1.plot(recent_data['date'], recent_data['spx_close'],
                linewidth=chart_config['line_width'], color=colors['primary_blue'], label='SPX Close')

        # Mark convergence events (show all events in the data range)
        if 'convergence_events' in self.analysis_results:
            convergence_events = self.analysis_results['convergence_events']
            if len(convergence_events) > 0:
                # Filter convergence events to the data range being shown
                data_start = recent_data['date'].min().date()
                data_end = recent_data['date'].max().date()
                visible_events = convergence_events[
                    (convergence_events['date'].dt.date >= data_start) &
                    (convergence_events['date'].dt.date <= data_end)
                ]
                for _, event in visible_events.iterrows():
                    color = colors['primary_red'] if event['event_type'] == 'historic_convergence' else colors['primary_orange']
                    ax1.axvline(x=event['date'], color=color, linestyle='--',
                               alpha=chart_config['line_alpha'],
                               label=f"{event['event_type'].replace('_', ' ').title()}")

        ax1.set_title('SPX Price with Recent Convergence Events')
        ax1.set_ylabel('SPX Price')
        ax1.legend()
        ax1.grid(True, alpha=chart_config['grid_alpha'])

        # Chart 2: Vomma and Vanna (recent trends)
        ax2 = axes[1]
        ax2_twin = ax2.twinx()

        line1 = ax2.plot(recent_data['date'], recent_data['Vomma_M'],
                        color=colors['primary_red'], linewidth=chart_config['line_width'], label='Vomma (M)')
        line2 = ax2_twin.plot(recent_data['date'], recent_data['Vanna_K'],
                             color=colors['primary_green'], linewidth=chart_config['line_width'], label='Vanna (K)')

        ax2.set_title('Vomma and Vanna Over Time')
        ax2.set_ylabel('Vomma (Millions)', color=colors['primary_red'])
        ax2_twin.set_ylabel('Vanna (Thousands)', color=colors['primary_green'])
        ax2.tick_params(axis='y', labelcolor=colors['primary_red'])
        ax2_twin.tick_params(axis='y', labelcolor=colors['primary_green'])

        # Combine legends
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='upper left')
        ax2.grid(True, alpha=chart_config['grid_alpha'])
        
        # Chart 3: Theta and Charm
        ax3 = axes[2]
        ax3_twin = ax3.twinx()

        line3 = ax3.plot(recent_data['date'], recent_data['Theta_M'],
                        color=colors['primary_purple'], linewidth=chart_config['line_width'], label='Theta (M)')
        line4 = ax3_twin.plot(recent_data['date'], recent_data['Charm_M'],
                             color=colors['primary_brown'], linewidth=chart_config['line_width'], label='Charm (M)')

        ax3.set_title('Theta and Charm Over Time')
        ax3.set_ylabel('Theta (Millions)', color=colors['primary_purple'])
        ax3_twin.set_ylabel('Charm (Millions)', color=colors['primary_brown'])
        ax3.tick_params(axis='y', labelcolor=colors['primary_purple'])
        ax3_twin.tick_params(axis='y', labelcolor=colors['primary_brown'])

        # Combine legends
        lines = line3 + line4
        labels = [l.get_label() for l in lines]
        ax3.legend(lines, labels, loc='upper left')
        ax3.grid(True, alpha=chart_config['grid_alpha'])
        
        # Chart 4: Open Interest and Volume
        ax4 = axes[3]
        ax4_twin = ax4.twinx()

        line5 = ax4.bar(recent_data['date'], recent_data['Total_OI_K'],
                       alpha=chart_config['bar_alpha'], color=colors['primary_cyan'], label='Open Interest (K)')
        line6 = ax4_twin.plot(recent_data['date'], recent_data['Volume'],
                             color=colors['primary_orange'], linewidth=chart_config['line_width'], label='Volume')

        ax4.set_title('Open Interest and Volume')
        ax4.set_ylabel('Open Interest (Thousands)', color=colors['primary_cyan'])
        ax4_twin.set_ylabel('Volume', color=colors['primary_orange'])
        ax4.tick_params(axis='y', labelcolor=colors['primary_cyan'])
        ax4_twin.tick_params(axis='y', labelcolor=colors['primary_orange'])
        ax4.set_xlabel('Date')

        # Format x-axis using configuration constants
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter(chart_config['date_format']))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=chart_config['date_interval_weekly']))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=chart_config['rotation_angle'])

        plt.tight_layout()

        # Save chart using configuration constants
        chart_path = self.config.get_output_path(chart_config['chart_files']['price_and_greeks'])
        plt.savefig(chart_path, dpi=chart_config['dpi'], bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def create_convergence_analysis_chart(self) -> str:
        """Create convergence events analysis chart showing all data with convergence events"""
        # Use configuration constants for chart dimensions and styling
        chart_config = self.config.CHART_CONFIG
        colors = chart_config['colors']

        # Check if we have convergence events to determine time window
        convergence_events = self.analysis_results.get('convergence_events', pd.DataFrame())

        if len(convergence_events) > 0:
            # Use a time window that includes all convergence events plus some context
            earliest_event = convergence_events['date'].min()
            latest_event = convergence_events['date'].max()

            # Add 10 days before earliest and 10 days after latest event for context
            start_date = (earliest_event - pd.Timedelta(days=10)).date()
            end_date = (latest_event + pd.Timedelta(days=10)).date()

            # Filter data to show convergence period with context
            recent_data = self.data[
                (self.data['date'].dt.date >= start_date) &
                (self.data['date'].dt.date <= end_date)
            ].copy()

            print(f"Convergence chart: showing data from {start_date} to {end_date} to include {len(convergence_events)} events")
        else:
            # If no convergence events, show recent data (last 30 days)
            today = pd.Timestamp.now().date()
            thirty_days_ago = today - pd.Timedelta(days=30)
            recent_data = self.data[self.data['date'].dt.date >= thirty_days_ago].copy()

        if len(recent_data) == 0:
            # If no data in range, use last 10 data points
            recent_data = self.data.tail(10).copy()

        fig, axes = plt.subplots(2, 2, figsize=chart_config['figure_size_medium'])
        fig.suptitle('📊 Convergence Events Analysis - RECENT TRENDS (Last 30 Days) 📊',
                    fontsize=chart_config['font_size_title'], fontweight='bold')

        # Chart 1: Extreme Score over time (recent)
        ax1 = axes[0, 0]
        ax1.plot(recent_data['date'], recent_data['extreme_score'],
                linewidth=chart_config['line_width'], color=colors['primary_red'],
                marker='o', markersize=chart_config['marker_size'])
        ax1.axhline(y=chart_config['convergence_threshold'], color=colors['primary_orange'],
                   linestyle='--', alpha=chart_config['line_alpha'], label='Convergence Threshold')
        ax1.axhline(y=chart_config['major_convergence_threshold'], color=colors['primary_red'],
                   linestyle='--', alpha=chart_config['line_alpha'], label='Major Convergence')
        ax1.set_title('Extreme Score Timeline')
        ax1.set_ylabel('Extreme Score')
        ax1.legend()
        ax1.grid(True, alpha=chart_config['grid_alpha'])
        
        # Chart 2: Vomma vs Price Change
        ax2 = axes[0, 1]
        scatter = ax2.scatter(self.data['price_change'], self.data['Vomma_M'],
                             c=self.data['extreme_score'], cmap='Reds',
                             alpha=chart_config['scatter_alpha'], s=chart_config['scatter_size'])
        ax2.set_title('Vomma vs Price Change')
        ax2.set_xlabel('Price Change')
        ax2.set_ylabel('Vomma (M)')
        ax2.grid(True, alpha=chart_config['grid_alpha'])
        plt.colorbar(scatter, ax=ax2, label='Extreme Score')

        # Chart 3: Delta P/C Ratio (show meaningful data range)
        ax3 = axes[1, 0]

        # Check if recent data has meaningful P/C ratio variation
        pc_variation = recent_data['Delta_PC_Ratio'].std()
        if pc_variation < 0.1:  # If very low variation, show broader range
            print(f"Low P/C variation ({pc_variation:.3f}) in recent data, expanding time window for P/C chart")
            # Use all available data for P/C ratio to show meaningful patterns
            pc_data = self.data[self.data['Delta_PC_Ratio'].notna() & (self.data['Delta_PC_Ratio'] != 0)].copy()
            if len(pc_data) > 30:
                pc_data = pc_data.tail(30)  # Show last 30 meaningful data points
        else:
            pc_data = recent_data

        ax3.plot(pc_data['date'], pc_data['Delta_PC_Ratio'],
                linewidth=chart_config['line_width'], color=colors['primary_green'])
        ax3.axhline(y=chart_config['delta_pc_bullish_threshold'], color=colors['primary_blue'],
                   linestyle='--', alpha=chart_config['line_alpha'], label='Bullish Threshold')
        ax3.axhline(y=chart_config['delta_pc_bearish_threshold'], color=colors['primary_red'],
                   linestyle='--', alpha=chart_config['line_alpha'], label='Bearish Threshold')
        ax3.set_title('Delta Put/Call Ratio (Meaningful Data Range)')
        ax3.set_ylabel('Delta P/C Ratio')
        ax3.legend()
        ax3.grid(True, alpha=chart_config['grid_alpha'])
        
        # Chart 4: Volume vs Extreme Events (recent data)
        ax4 = axes[1, 1]
        # Use configuration constants for color mapping
        major_threshold = chart_config['major_convergence_threshold']
        convergence_threshold = chart_config['convergence_threshold']
        scatter_colors = [colors['primary_red'] if score >= major_threshold
                         else colors['primary_orange'] if score >= convergence_threshold
                         else colors['primary_blue']
                         for score in recent_data['extreme_score']]
        ax4.scatter(recent_data['Volume'], recent_data['Total_OI_K'], c=scatter_colors,
                   alpha=chart_config['scatter_alpha'], s=chart_config['scatter_size'])
        ax4.set_title('Volume vs Open Interest (colored by extreme score)')
        ax4.set_xlabel('Volume')
        ax4.set_ylabel('Open Interest (K)')
        ax4.grid(True, alpha=chart_config['grid_alpha'])

        # Format x-axis for time series using configuration constants
        for ax in [ax1, ax3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter(chart_config['date_format']))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=chart_config['date_interval_weekly']))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=chart_config['rotation_angle'])

        plt.tight_layout()

        # Save chart using configuration constants
        chart_path = self.config.get_output_path(chart_config['chart_files']['convergence_analysis'])
        plt.savefig(chart_path, dpi=chart_config['dpi'], bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def create_correlation_heatmap(self) -> str:
        """Create correlation heatmap for Greeks and price"""
        if 'correlations' not in self.analysis_results:
            return None
            
        correlation_matrix = self.analysis_results['correlations']['correlation_matrix']
        
        # Select key variables for heatmap
        key_vars = ['spx_close', 'Vomma_M', 'Vanna_K', 'Charm_M', 'Theta_M', 
                   'Delta_M', 'Gamma_M', 'Vega_M', 'Total_OI_K', 'Volume', 'Delta_PC_Ratio']
        
        # Filter correlation matrix
        filtered_corr = correlation_matrix.loc[key_vars, key_vars]
        
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # Create heatmap
        sns.heatmap(filtered_corr, annot=True, cmap='RdBu_r', center=0, 
                   square=True, fmt='.3f', cbar_kws={'label': 'Correlation'})
        
        plt.title('Greeks and Price Correlation Matrix', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # Save chart
        chart_path = self.config.get_output_path('correlation_heatmap.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def create_regime_analysis_chart(self) -> str:
        """Create market regime analysis chart focused on recent trends and current regime"""
        if 'regimes' not in self.analysis_results:
            return None

        regime_data = self.analysis_results['regimes']['regime_data']

        # Focus on recent data (last 45 days) to show current regime trends
        today = pd.Timestamp.now().date()
        forty_five_days_ago = today - pd.Timedelta(days=45)
        recent_regime_data = regime_data[regime_data['date'].dt.date >= forty_five_days_ago].copy()

        if len(recent_regime_data) == 0:
            # If no recent data, use last 20 data points
            recent_regime_data = regime_data.tail(20).copy()

        fig, axes = plt.subplots(2, 1, figsize=(15, 10))
        fig.suptitle('📊 Current Market Regime Analysis - Recent Trends 📊', fontsize=16, fontweight='bold')

        # Chart 1: Recent Price with Greek regimes
        ax1 = axes[0]

        # Color code by Greek regime
        regime_colors = {'normal': 'blue', 'extreme': 'red', 'distribution': 'orange', 'collapse': 'purple'}

        for regime in recent_regime_data['greek_regime'].unique():
            regime_subset = recent_regime_data[recent_regime_data['greek_regime'] == regime]
            ax1.scatter(regime_subset['date'], regime_subset['spx_close'],
                       c=regime_colors.get(regime, 'gray'), label=f'{regime.title()} Regime',
                       alpha=0.7, s=50)

        # Add trend line for recent price movement
        ax1.plot(recent_regime_data['date'], recent_regime_data['spx_close'],
                color='black', alpha=0.3, linewidth=1, label='Price Trend')

        ax1.set_title('Recent SPX Price by Greek Regime (Current Market State)')
        ax1.set_ylabel('SPX Price')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Chart 2: Recent Volatility regimes
        ax2 = axes[1]

        vol_colors = {'low_vol': 'green', 'normal': 'blue', 'high_vol': 'red'}

        for regime in recent_regime_data['volatility_regime'].unique():
            regime_subset = recent_regime_data[recent_regime_data['volatility_regime'] == regime]
            ax2.scatter(regime_subset['date'], regime_subset['spx_close'],
                       c=vol_colors.get(regime, 'gray'), label=f'{regime.replace("_", " ").title()}',
                       alpha=0.7, s=50)

        # Add trend line for recent price movement
        ax2.plot(recent_regime_data['date'], recent_regime_data['spx_close'],
                color='black', alpha=0.3, linewidth=1, label='Price Trend')

        ax2.set_title('Recent SPX Price by Volatility Regime (Current Market State)')
        ax2.set_ylabel('SPX Price')
        ax2.set_xlabel('Date')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Format x-axis for recent data view
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d/%Y'))  # Include year for clarity
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))  # Weekly intervals for recent data
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # Add current regime status as subtitle
        current_regime = recent_regime_data.iloc[-1] if len(recent_regime_data) > 0 else None
        if current_regime is not None:
            fig.text(0.5, 0.93, f'Current State: {current_regime["greek_regime"].title()} Greek Regime, {current_regime["volatility_regime"].replace("_", " ").title()} Volatility',
                    ha='center', fontsize=12, style='italic', color='red', weight='bold')
        
        plt.tight_layout()
        
        # Save chart
        chart_path = self.config.get_output_path('regime_analysis_chart.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def generate_markdown_report(self) -> str:
        """Generate comprehensive markdown report"""
        report_path = self.config.get_output_path('spx_greeks_analysis_report.md')
        
        # Start with title and summary
        with open(report_path, 'w') as f:
            f.write(f"# SPX Options Greeks Analysis Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**Analysis Period:** {self.data['date'].min().strftime('%Y-%m-%d')} to {self.data['date'].max().strftime('%Y-%m-%d')}\n\n")
            f.write(f"**Total Days Analyzed:** {len(self.data)}\n\n")
            f.write("---\n\n")
        
        # Executive Summary
        self._append_executive_summary(report_path)
        
        # Key Findings
        self._append_key_findings(report_path)
        
        # Convergence Events Analysis
        self._append_convergence_analysis(report_path)
        
        # Vomma/Price Divergence Analysis
        self._append_divergence_analysis(report_path)
        
        # Market Regime Analysis
        self._append_regime_analysis(report_path)
        
        # Charts section
        self._append_charts_section(report_path)
        
        # Technical Details
        self._append_technical_details(report_path)
        
        return str(report_path)
    
    def _append_executive_summary(self, report_path: str):
        """Append executive summary section with ChatGPT narrative"""
        summary = self.analysis_results.get('summary', {})
        key_findings = summary.get('key_findings', {})

        # Generate dynamic narrative using ChatGPT
        if self.narrative_generator:
            try:
                print("Generating executive summary narrative with ChatGPT...")
                narrative = self.narrative_generator.generate_executive_summary(self.analysis_results)

                content = f"""## Executive Summary

{narrative}

### Key Metrics:
- **Total Convergence Events:** {key_findings.get('total_convergence_events', 0)}
- **Distribution Signals:** {key_findings.get('distribution_signals', 0)}
- **Volume Collapse Events:** {key_findings.get('volume_collapse_events', 0)}
- **Extreme Greek Days:** {key_findings.get('extreme_greek_days', 0)}

"""
            except Exception as e:
                print(f"Error generating ChatGPT narrative, using fallback: {e}")
                content = self._fallback_executive_summary(key_findings)
        else:
            content = self._fallback_executive_summary(key_findings)

        with open(report_path, 'a') as f:
            f.write(content)

    def _fallback_executive_summary(self, key_findings: dict) -> str:
        """Fallback executive summary when ChatGPT is unavailable"""
        return f"""## Executive Summary

This analysis examines SPX options Greeks patterns from {self.data['date'].min().strftime('%B %Y')} to {self.data['date'].max().strftime('%B %Y')}, focusing on extreme events and convergence patterns that indicate potential market manipulation or engineered moves.

### Key Metrics:
- **Total Convergence Events:** {key_findings.get('total_convergence_events', 0)}
- **Distribution Signals:** {key_findings.get('distribution_signals', 0)}
- **Volume Collapse Events:** {key_findings.get('volume_collapse_events', 0)}
- **Extreme Greek Days:** {key_findings.get('extreme_greek_days', 0)}

### Market Behavior:
The analysis reveals {len(self.data[self.data['extreme_score'] >= 4])} days with extreme Greek positioning, suggesting periods of heightened institutional activity and potential market engineering.

"""
    
    def _append_key_findings(self, report_path: str):
        """Append key findings section"""
        content = """## Key Findings

### 1. Convergence Events
"""
        
        if 'convergence_events' in self.analysis_results:
            convergence_events = self.analysis_results['convergence_events']
            if len(convergence_events) > 0:
                content += f"Identified {len(convergence_events)} convergence events where multiple Greeks reached extreme levels simultaneously:\n\n"
                
                for _, event in convergence_events.iterrows():
                    content += f"- **{event['date'].strftime('%Y-%m-%d')}**: {event['event_type'].replace('_', ' ').title()} "
                    content += f"(Score: {event['extreme_score']}, SPX: {event['spx_close']:.2f})\n"
                
                content += "\n"
            else:
                content += "No significant convergence events detected in this period.\n\n"
        
        content += """### 2. Vomma/Price Divergence Patterns
"""
        
        if 'vomma_divergence' in self.analysis_results:
            divergence = self.analysis_results['vomma_divergence']
            content += f"- Distribution signals detected: {len(divergence['distribution_signals'])}\n"
            content += f"- Acceleration signals detected: {len(divergence['acceleration_signals'])}\n"
            content += f"- Vomma-Price correlation: {divergence['correlation_vomma_price']:.3f}\n\n"
        
        content += """### 3. Market Regime Classification
"""
        
        if 'regimes' in self.analysis_results:
            regimes = self.analysis_results['regimes']
            content += "**Volatility Regimes:**\n"
            for regime, count in regimes['volatility_regimes'].items():
                content += f"- {regime.replace('_', ' ').title()}: {count} days\n"
            
            content += "\n**Greek Regimes:**\n"
            for regime, count in regimes['greek_regimes'].items():
                content += f"- {regime.replace('_', ' ').title()}: {count} days\n"
            content += "\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_convergence_analysis(self, report_path: str):
        """Append detailed convergence analysis with ChatGPT narrative"""
        # Get data-driven thresholds for accurate reporting
        thresholds = self.analysis_results.get('convergence_thresholds', {})
        vomma_threshold = thresholds.get('vomma_threshold', 0)
        vanna_threshold = thresholds.get('vanna_threshold', 0)

        content = f"""## Convergence Events Analysis

Convergence events occur when multiple Greeks reach extreme levels simultaneously, often indicating institutional positioning or market engineering. This analysis uses data-driven thresholds based on statistical percentiles rather than arbitrary values.

### Data-Driven Scoring System:
- **Extreme Vomma (>{vomma_threshold:.1f}M):** +{self.config.DATA_DRIVEN_CONFIG['vomma_score_weight']} points
- **Extreme Vanna (>{vanna_threshold:.1f}K):** +{self.config.DATA_DRIVEN_CONFIG['vanna_score_weight']} points
- **Extreme Charm:** +{self.config.DATA_DRIVEN_CONFIG['charm_score_weight']} points
- **Extreme Theta:** +{self.config.DATA_DRIVEN_CONFIG['theta_score_weight']} points
- **High Open Interest:** +{self.config.DATA_DRIVEN_CONFIG['oi_score_weight']} point
- **Extreme Delta P/C Ratio:** +{self.config.DATA_DRIVEN_CONFIG['delta_pc_score_weight']} point

### Event Classifications:
- **Convergence (Score {self.config.DATA_DRIVEN_CONFIG['convergence_min_score']}-{self.config.DATA_DRIVEN_CONFIG['major_convergence_min_score']-1}):** Multiple extremes present
- **Major Convergence (Score {self.config.DATA_DRIVEN_CONFIG['major_convergence_min_score']}-{self.config.DATA_DRIVEN_CONFIG['historic_convergence_min_score']-1}):** Significant institutional activity
- **Historic Convergence (Score {self.config.DATA_DRIVEN_CONFIG['historic_convergence_min_score']}+):** Unprecedented positioning

"""

        # Add ChatGPT narrative
        if self.narrative_generator:
            try:
                print("Generating convergence analysis narrative with ChatGPT...")
                narrative = self.narrative_generator.generate_convergence_narrative(self.analysis_results)
                if narrative and narrative.strip():
                    content += f"### Market Analysis:\n\n{narrative}\n\n"
                    print(f"✓ Convergence narrative added ({len(narrative)} chars)")
                else:
                    print("⚠ Empty convergence narrative received")
            except Exception as e:
                print(f"Error generating convergence narrative: {e}")
                import traceback
                traceback.print_exc()

        if 'convergence_events' in self.analysis_results:
            convergence_events = self.analysis_results['convergence_events']
            if len(convergence_events) > 0:
                content += "### Detailed Event Analysis:\n\n"

                for _, event in convergence_events.iterrows():
                    content += f"#### {event['date'].strftime('%Y-%m-%d')} - {event['event_type'].replace('_', ' ').title()}\n"
                    content += f"- **Extreme Score:** {event['extreme_score']}\n"
                    content += f"- **SPX Price:** {event['spx_close']:.2f}\n"
                    content += f"- **Vomma:** {event['Vomma_M']:.2f}M\n"
                    content += f"- **Vanna:** {event['Vanna_K']:.2f}K\n"
                    content += f"- **Open Interest:** {event['Total_OI_K']:.2f}K\n"
                    content += f"- **Delta P/C Ratio:** {event['Delta_PC_Ratio']:.3f}\n\n"

        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_divergence_analysis(self, report_path: str):
        """Append Vomma/Price divergence analysis"""
        content = """## Vomma/Price Divergence Analysis

Vomma (volatility of volatility) divergence from price movements can signal distribution or acceleration patterns:

### Distribution Pattern:
- Price rises while Vomma explodes higher
- Indicates institutional distribution into retail buying
- Creates perfect trap for late buyers

### Acceleration Pattern:  
- Price falls with negative Vomma
- Creates self-reinforcing decline
- Amplifies volatility impact

"""
        
        if 'vomma_divergence' in self.analysis_results:
            divergence = self.analysis_results['vomma_divergence']
            
            content += f"### Analysis Results:\n"
            content += f"- **Vomma-Price Correlation:** {divergence['correlation_vomma_price']:.3f}\n"
            content += f"- **Distribution Signals:** {len(divergence['distribution_signals'])}\n"
            content += f"- **Acceleration Signals:** {len(divergence['acceleration_signals'])}\n\n"
            
            if len(divergence['distribution_signals']) > 0:
                content += "### Distribution Signal Details:\n\n"
                for _, signal in divergence['distribution_signals'].head(3).iterrows():
                    content += f"- **{signal['date'].strftime('%Y-%m-%d')}**: Price +{signal['price_change']:.2f}, "
                    content += f"Vomma {signal['Vomma_M']:.2f}M (+{signal['vomma_change']:.2f}M)\n"
                content += "\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_regime_analysis(self, report_path: str):
        """Append market regime analysis with ChatGPT narrative"""
        content = """## Market Regime Analysis

Market regimes are classified based on volatility levels and Greek extremes using data-driven thresholds:

### Volatility Regimes:
- **Low Vol:** Bottom 20% of Vega readings
- **Normal:** Middle 60% of Vega readings
- **High Vol:** Top 20% of Vega readings

### Greek Regimes:
- **Normal:** Standard Greek levels
- **Extreme:** Multiple Greeks at extreme levels (85th percentile)
- **Distribution:** Price up + high Greeks (75th percentile)
- **Collapse:** Low Vomma + price down (25th percentile)

"""

        if 'regimes' in self.analysis_results:
            regimes = self.analysis_results['regimes']

            content += "### Regime Distribution:\n\n"
            content += "**Volatility Regimes:**\n"
            total_days = sum(regimes['volatility_regimes'].values())
            for regime, count in regimes['volatility_regimes'].items():
                pct = (count / total_days) * 100
                content += f"- {regime.replace('_', ' ').title()}: {count} days ({pct:.1f}%)\n"

            content += "\n**Greek Regimes:**\n"
            total_days = sum(regimes['greek_regimes'].values())
            for regime, count in regimes['greek_regimes'].items():
                pct = (count / total_days) * 100
                content += f"- {regime.replace('_', ' ').title()}: {count} days ({pct:.1f}%)\n"
            content += "\n"

            # Add ChatGPT narrative
            if self.narrative_generator:
                try:
                    print("Generating regime analysis narrative with ChatGPT...")
                    narrative = self.narrative_generator.generate_regime_narrative(self.analysis_results)
                    content += f"### Market Interpretation:\n\n{narrative}\n\n"
                except Exception as e:
                    print(f"Error generating regime narrative: {e}")

        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_charts_section(self, report_path: str):
        """Append charts section"""
        content = """## Charts and Visualizations

The following charts provide visual analysis of the Greeks patterns and market behavior:

"""
        
        # Use chart descriptions from configuration
        chart_descriptions = self.config.CHART_CONFIG['chart_descriptions']
        
        for chart_path in self.chart_paths:
            chart_name = Path(chart_path).name
            if chart_name in chart_descriptions:
                content += f"### {chart_descriptions[chart_name]}\n\n"
                content += f"![{chart_descriptions[chart_name]}]({chart_name})\n\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_technical_details(self, report_path: str):
        """Append technical details section"""
        content = """## Chart Types: Historical vs Future Projection

**Two Analysis Perspectives:**

### 1. Historical Trading Analysis (Time Series Charts)
- **X-Axis**: Trading dates when options data was recorded (April-June 2025)
- **Purpose**: Shows how Greeks evolved during the trading period
- **Use Case**: Understanding past market behavior and patterns

### 2. Future Price Projection Analysis (Expiry-Focused Charts)
- **X-Axis**: Option expiry dates (future dates beyond analysis period)
- **Purpose**: Projects potential price impact based on option expirations
- **Use Case**: Forecasting future price movements and volatility events

**Key Insight**: The "Future Price Projection" chart groups all options by their expiry dates to show when Greeks exposure will impact the market, regardless of when the options were traded.

**Filtering Applied**: Only options with expiry dates AFTER the analysis date are included (no expired options).

## Technical Details

### Data Processing:
- **Source:** SPX options chain data Q2 2025
- **Greeks Calculated:** Delta, Gamma, Vega, Theta, Rho
- **Derived Greeks:** Vomma, Vanna, Charm
- **Aggregation:** Daily totals weighted by Open Interest

### Methodology:
- **Vomma Calculation:** Vega × Gamma × (Price/100)
- **Vanna Calculation:** Vega × Gamma / Price  
- **Charm Calculation:** -Theta × Gamma / Price
- **Extreme Thresholds:** Based on statistical analysis of historical data

### Risk Considerations:
- Analysis based on historical data patterns
- Market conditions can change rapidly
- Greeks calculations are approximations
- Past patterns may not predict future behavior

---

*This report was generated using the SPX Greeks Analysis Engine*
"""
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def create_expiry_focused_analysis_chart(self) -> str:
        """Create future-focused analysis chart grouped by expiry dates for price projection"""
        # We need access to the raw processed data to analyze by expiry dates
        if 'raw_data_sample' not in self.analysis_results:
            return None

        raw_data = self.analysis_results['raw_data_sample']

        # Filter for FUTURE expiry dates only using configuration constants
        chart_config = self.config.CHART_CONFIG
        future_days = chart_config['future_analysis_days']  # 30 days from config

        today = pd.Timestamp.now().date()
        thirty_days_out = today + pd.Timedelta(days=future_days)

        future_data = raw_data[
            (raw_data['Expiry Date'].dt.date > today) &
            (raw_data['Expiry Date'].dt.date <= thirty_days_out)
        ].copy()

        if len(future_data) == 0:
            print(f"Warning: No expiry dates found in next 30 days ({today} to {thirty_days_out})")
            return None

        # Group data by expiry date and aggregate Greeks
        expiry_aggregates = future_data.groupby('Expiry Date').agg({
            'Vomma': 'sum',
            'Vanna': 'sum',
            'Charm': 'sum',
            'Theta': 'sum',
            'Open Interest': 'sum',
            'Strike': 'mean',  # Average strike for reference
            'spx_close': 'first',  # SPX price (should be same for same trading day)
            'DTE': 'first'  # Days to expiration
        }).reset_index()

        # Convert to same scale as daily aggregates using configuration constants
        million_scale = chart_config['million_scale']
        thousand_scale = chart_config['thousand_scale']

        expiry_aggregates['Vomma_M'] = expiry_aggregates['Vomma'] / million_scale
        expiry_aggregates['Vanna_K'] = expiry_aggregates['Vanna'] / thousand_scale
        expiry_aggregates['Charm_M'] = expiry_aggregates['Charm'] / million_scale
        expiry_aggregates['Theta_M'] = expiry_aggregates['Theta'] / million_scale
        expiry_aggregates['Total_OI_K'] = expiry_aggregates['Open Interest'] / thousand_scale

        # Sort by expiry date for future projection
        expiry_aggregates = expiry_aggregates.sort_values('Expiry Date')

        # Calculate projected price impact based on Greeks using configuration constants
        current_spx = expiry_aggregates['spx_close'].iloc[0]  # Current SPX level
        impact_factors = chart_config['projection_impact_factors']

        expiry_aggregates['Projected_Impact'] = (
            expiry_aggregates['Vomma_M'] * impact_factors['vomma_factor'] +    # Vomma impact factor
            expiry_aggregates['Vanna_K'] * impact_factors['vanna_factor'] +    # Vanna impact factor
            expiry_aggregates['Charm_M'] * impact_factors['charm_factor']      # Charm decay factor (negative)
        )
        expiry_aggregates['Projected_SPX'] = current_spx + expiry_aggregates['Projected_Impact']

        fig, axes = plt.subplots(4, 1, figsize=(15, 18))
        fig.suptitle(f'📈 NEXT 30 DAYS: Greeks by Option Expiry Date ({today} → {thirty_days_out}) 📈',
                    fontsize=16, fontweight='bold', color='darkblue')

        # Add today's date line for reference
        today_line = pd.Timestamp.now()

        # Chart 1: SPX Price Projection Based on Greeks
        ax1 = axes[0]
        ax1_twin = ax1.twinx()

        # Current SPX level
        current_spx = expiry_aggregates['spx_close'].iloc[0]
        ax1.axhline(y=current_spx, color='black', linestyle='--', alpha=0.7, label=f'Current SPX: {current_spx:.0f}')

        line1 = ax1.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Projected_SPX'],
                        color='blue', linewidth=3, marker='o', markersize=8, label='Projected SPX Price')
        line2 = ax1_twin.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Projected_Impact'],
                             color='orange', linewidth=2, marker='s', label='Greeks Impact')

        ax1.set_title(f'📈 SPX Price Projection - Next 30 Days ({today} → {thirty_days_out})', fontweight='bold', color='darkblue')
        ax1.set_ylabel('SPX Price Level', color='blue', fontweight='bold')
        ax1_twin.set_ylabel('Greeks Impact Factor', color='orange')
        ax1.tick_params(axis='y', labelcolor='blue')
        ax1_twin.tick_params(axis='y', labelcolor='orange')

        # Add vertical line for today
        ax1.axvline(x=today_line, color='red', linestyle=':', linewidth=3, alpha=0.9, label=f'TODAY ({today})')

        # Combine legends
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # Chart 2: Vomma and Vanna by Future Expiry Date
        ax2 = axes[1]
        ax2_twin = ax2.twinx()

        line3 = ax2.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Vomma_M'],
                        color='red', linewidth=2, marker='o', label='Vomma (M)')
        line4 = ax2_twin.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Vanna_K'],
                             color='green', linewidth=2, marker='s', label='Vanna (K)')

        ax2.set_title(f'📈 Vomma & Vanna - Next 30 Days ({today} → {thirty_days_out})', color='darkblue')
        ax2.set_ylabel('Vomma (Millions)', color='red')
        ax2_twin.set_ylabel('Vanna (Thousands)', color='green')
        ax2.tick_params(axis='y', labelcolor='red')
        ax2_twin.tick_params(axis='y', labelcolor='green')

        # Add vertical line for today
        ax2.axvline(x=today_line, color='red', linestyle=':', linewidth=3, alpha=0.9, label=f'TODAY ({today})')

        # Combine legends
        lines = line3 + line4
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='upper left')
        ax2.grid(True, alpha=0.3)

        # Chart 3: Theta and Charm by Future Expiry Date
        ax3 = axes[2]
        ax3_twin = ax3.twinx()

        line5 = ax3.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Theta_M'],
                        color='purple', linewidth=2, marker='o', label='Theta (M)')
        line6 = ax3_twin.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['Charm_M'],
                             color='brown', linewidth=2, marker='s', label='Charm (M)')

        ax3.set_title(f'📈 Theta & Charm - Next 30 Days ({today} → {thirty_days_out})', color='darkblue')
        ax3.set_ylabel('Theta (Millions)', color='purple')
        ax3_twin.set_ylabel('Charm (Millions)', color='brown')
        ax3.tick_params(axis='y', labelcolor='purple')
        ax3_twin.tick_params(axis='y', labelcolor='brown')

        # Add vertical line for today
        ax3.axvline(x=today_line, color='red', linestyle=':', linewidth=3, alpha=0.9, label=f'TODAY ({today})')

        # Combine legends
        lines = line5 + line6
        labels = [l.get_label() for l in lines]
        ax3.legend(lines, labels, loc='upper left')
        ax3.grid(True, alpha=0.3)

        # Chart 4: Future Open Interest and Days to Expiration
        ax4 = axes[3]
        ax4_twin = ax4.twinx()

        bars = ax4.bar(expiry_aggregates['Expiry Date'], expiry_aggregates['Total_OI_K'],
                      alpha=0.6, color='cyan', label='Open Interest (K)')
        line7 = ax4_twin.plot(expiry_aggregates['Expiry Date'], expiry_aggregates['DTE'],
                             color='orange', linewidth=2, marker='o', label='Days to Expiration')

        ax4.set_title(f'📈 Open Interest & DTE - Next 30 Days ({today} → {thirty_days_out})', color='darkblue')
        ax4.set_ylabel('Open Interest (Thousands)', color='cyan')
        ax4_twin.set_ylabel('Days to Expiration', color='orange')
        ax4.set_xlabel(f'📈 Option Expiry Dates - Next 30 Days ({today} → {thirty_days_out})')
        ax4.tick_params(axis='y', labelcolor='cyan')
        ax4_twin.tick_params(axis='y', labelcolor='orange')

        # Add vertical line for today
        ax4.axvline(x=today_line, color='red', linestyle=':', linewidth=3, alpha=0.9, label=f'TODAY ({today})')

        # Format x-axis for all charts - Daily intervals for 30-day view
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))  # Month/day for 30-day view
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=3))  # Every 3 days for 30-day period
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # Add subtitle explaining 30-day focus
        fig.text(0.5, 0.95, f'📈 NEXT 30 DAYS ANALYSIS - Expiry dates from {today} to {thirty_days_out} 📈',
                ha='center', fontsize=12, style='italic', color='red', weight='bold')

        plt.tight_layout()

        # Save chart
        chart_path = self.config.get_output_path('expiry_focused_analysis_chart.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        self.chart_paths.append(chart_path)
        return str(chart_path)

    def create_expiry_distribution_chart(self) -> str:
        """Create chart showing distribution of FUTURE option expiry dates"""
        # We need access to the raw processed data to show expiry dates
        if 'raw_data_sample' not in self.analysis_results:
            return None

        raw_data = self.analysis_results['raw_data_sample']

        # Filter for FUTURE expiry dates only (next 30 days)
        today = pd.Timestamp.now().date()
        thirty_days_out = today + pd.Timedelta(days=30)

        future_data = raw_data[
            (raw_data['Expiry Date'].dt.date > today) &
            (raw_data['Expiry Date'].dt.date <= thirty_days_out)
        ].copy()

        if len(future_data) == 0:
            print(f"Warning: No expiry dates found in next 30 days ({today} to {thirty_days_out})")
            return None

        fig, axes = plt.subplots(2, 1, figsize=(15, 10))
        fig.suptitle(f'📈 NEXT 30 DAYS: Options Expiry Analysis ({today} → {thirty_days_out})',
                    fontsize=16, fontweight='bold', color='darkblue')

        # Chart 1: Future expiry date distribution
        ax1 = axes[0]
        expiry_counts = future_data['Expiry Date'].value_counts().sort_index()
        bars1 = ax1.bar(expiry_counts.index, expiry_counts.values, alpha=0.7, color='skyblue')
        ax1.set_title('Distribution of FUTURE Option Expiry Dates (After Today)', fontweight='bold')
        ax1.set_ylabel('Number of Options')
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)

        # Add vertical line for today
        today_line = pd.Timestamp.now()
        ax1.axvline(x=today_line, color='red', linestyle='--', linewidth=2, alpha=0.8, label='Today')
        ax1.legend()

        # Chart 2: Future days to expiration distribution
        ax2 = axes[1]
        dte_counts = future_data['DTE'].value_counts().sort_index()
        bars2 = ax2.bar(dte_counts.index, dte_counts.values, alpha=0.7, color='lightgreen')
        ax2.set_title('Distribution of FUTURE Days to Expiration (DTE > 0)', fontweight='bold')
        ax2.set_xlabel('Days to Expiration')
        ax2.set_ylabel('Number of Options')
        ax2.grid(True, alpha=0.3)

        # Add text annotations for future focus
        min_expiry = future_data['Expiry Date'].min().date()
        max_expiry = future_data['Expiry Date'].max().date()

        ax1.text(0.02, 0.98, f'Analysis Date: {today}\nEarliest Expiry: {min_expiry}\nLatest Expiry: {max_expiry}',
                transform=ax1.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        # Save chart
        chart_path = self.config.get_output_path('expiry_distribution_chart.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        self.chart_paths.append(chart_path)
        return str(chart_path)

    def generate_pdf_report(self) -> str:
        """Generate PDF report from markdown using weasyprint"""
        # First generate all charts
        print("Generating charts...")
        self.create_price_and_greeks_chart()
        self.create_convergence_analysis_chart()
        self.create_correlation_heatmap()
        self.create_regime_analysis_chart()
        self.create_expiry_focused_analysis_chart()  # New future-focused chart
        self.create_expiry_distribution_chart()

        # Generate markdown report
        print("Generating markdown report...")
        md_path = self.generate_markdown_report()

        # Convert to PDF using weasyprint (more reliable than manus)
        print("Converting to PDF...")
        pdf_path = self.config.get_output_path('spx_greeks_analysis_report.pdf')

        try:
            # Use reportlab directly (more reliable on macOS)
            print("Generating PDF with reportlab...")
            return self._generate_pdf_with_reportlab(md_path, pdf_path)

        except ImportError:
            print("reportlab not available, trying weasyprint...")
            try:
                # Fallback to weasyprint
                import weasyprint

                # Read markdown and convert to HTML first
                with open(md_path, 'r') as f:
                    markdown_content = f.read()

                # Simple markdown to HTML conversion for basic formatting
                html_content = self._markdown_to_html(markdown_content)

                # Generate PDF from HTML
                weasyprint.HTML(string=html_content, base_url=str(self.config.REPORTS_DIR)).write_pdf(pdf_path)
                print(f"✓ PDF report generated successfully: {os.path.basename(pdf_path)}")
                return str(pdf_path)

            except Exception as e:
                print(f"Error with weasyprint: {e}")
                print("Returning markdown report instead")
                return str(md_path)

        except Exception as e:
            print(f"Error generating PDF: {e}")
            print("Returning markdown report instead")
            return str(md_path)
    
    def _markdown_to_html(self, markdown_content: str) -> str:
        """Convert basic markdown to HTML for PDF generation"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>SPX Greeks Analysis Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
                h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; }}
                h2 {{ color: #34495e; border-bottom: 1px solid #bdc3c7; }}
                h3 {{ color: #7f8c8d; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                img {{ max-width: 100%; height: auto; margin: 20px 0; }}
                .metric {{ font-weight: bold; color: #e74c3c; }}
            </style>
        </head>
        <body>
        """

        # Basic markdown to HTML conversion
        lines = markdown_content.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                html += f"<h1>{line[2:]}</h1>\n"
            elif line.startswith('## '):
                html += f"<h2>{line[3:]}</h2>\n"
            elif line.startswith('### '):
                html += f"<h3>{line[4:]}</h3>\n"
            elif line.startswith('**') and line.endswith('**'):
                html += f"<p class='metric'>{line[2:-2]}</p>\n"
            elif line.startswith('- '):
                html += f"<li>{line[2:]}</li>\n"
            elif line == '---':
                html += "<hr>\n"
            elif line:
                html += f"<p>{line}</p>\n"
            else:
                html += "<br>\n"

        html += "</body></html>"
        return html

    def _generate_pdf_with_reportlab(self, md_path: str, pdf_path: str) -> str:
        """Enhanced PDF generation using reportlab with chart images"""
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors

        doc = SimpleDocTemplate(str(pdf_path), pagesize=A4,
                               rightMargin=72, leftMargin=72,
                               topMargin=72, bottomMargin=18)
        styles = getSampleStyleSheet()
        story = []

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.darkblue,
            alignment=1  # Center alignment
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.darkblue
        )

        with open(md_path, 'r') as f:
            content = f.read()

        # Add title
        story.append(Paragraph("📊 SPX Options Greeks Analysis Report 📊", title_style))
        story.append(Spacer(1, 20))

        # Process content line by line
        lines = content.split('\n')
        i = 0
        while i < len(lines):
            line = lines[i].strip()

            if line:
                if line.startswith('# '):
                    # Main heading
                    story.append(Paragraph(line.replace('#', '').strip(), title_style))
                    story.append(Spacer(1, 12))
                elif line.startswith('## '):
                    # Section heading
                    story.append(Paragraph(line.replace('##', '').strip(), heading_style))
                    story.append(Spacer(1, 8))
                elif line.startswith('### '):
                    # Subsection heading
                    story.append(Paragraph(line.replace('###', '').strip(), styles['Heading3']))
                    story.append(Spacer(1, 6))
                elif line.startswith('![') and '](' in line and line.endswith(')'):
                    # Image reference - embed the actual chart
                    try:
                        # Extract image filename from markdown syntax
                        start = line.find('](') + 2
                        end = line.find(')', start)
                        image_name = line[start:end]

                        # Find the full path to the image
                        image_path = None
                        for chart_path in self.chart_paths:
                            chart_path_str = str(chart_path)  # Convert PosixPath to string
                            if chart_path_str.endswith(image_name):
                                image_path = chart_path_str
                                break

                        if image_path and os.path.exists(image_path):
                            # Add the image to PDF
                            img = Image(image_path, width=6*inch, height=4*inch)
                            story.append(img)
                            story.append(Spacer(1, 12))
                            print(f"✓ Added chart to PDF: {image_name}")
                        else:
                            # Fallback to text if image not found
                            story.append(Paragraph(f"Chart: {image_name}", styles['Normal']))
                            story.append(Spacer(1, 6))
                    except Exception as e:
                        print(f"Error adding image {line}: {e}")
                        story.append(Paragraph(line, styles['Normal']))
                        story.append(Spacer(1, 6))
                else:
                    # Regular text
                    if line.startswith('**') and line.endswith('**'):
                        # Bold text
                        story.append(Paragraph(f"<b>{line[2:-2]}</b>", styles['Normal']))
                    elif line.startswith('- '):
                        # Bullet point
                        story.append(Paragraph(f"• {line[2:]}", styles['Normal']))
                    else:
                        story.append(Paragraph(line, styles['Normal']))
                    story.append(Spacer(1, 6))

            i += 1

        # Add page break before charts section
        if len(self.chart_paths) > 0:
            story.append(PageBreak())
            story.append(Paragraph("📈 Additional Charts and Visualizations 📈", heading_style))
            story.append(Spacer(1, 12))

            # Add any remaining charts that weren't embedded in markdown
            for chart_path in self.chart_paths:
                chart_path_str = str(chart_path)  # Convert PosixPath to string
                chart_name = os.path.basename(chart_path_str)
                if os.path.exists(chart_path_str):
                    story.append(Paragraph(f"Chart: {chart_name}", styles['Heading3']))
                    story.append(Spacer(1, 6))
                    img = Image(chart_path_str, width=6*inch, height=4*inch)
                    story.append(img)
                    story.append(Spacer(1, 12))

        doc.build(story)
        print(f"✓ PDF report generated with reportlab and embedded charts: {os.path.basename(pdf_path)}")
        return str(pdf_path)

    def get_chart_paths(self) -> List[str]:
        """Get list of generated chart paths"""
        return self.chart_paths

