"""
Report Generator for SPX Options Greeks Analysis
Creates comprehensive charts and PDF reports
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class GreeksReportGenerator:
    """Report generator for Greeks analysis with charts and PDF output"""
    
    def __init__(self, config):
        self.config = config
        self.data = None
        self.analysis_results = None
        self.chart_paths = []
        
    def set_data(self, daily_data: pd.DataFrame, analysis_results: Dict):
        """Set the data and analysis results for report generation"""
        # Use processed data with extreme scores if available
        if 'processed_data' in analysis_results:
            self.data = analysis_results['processed_data'].copy()
        else:
            self.data = daily_data.copy()
            # Add basic extreme_score if not present
            if 'extreme_score' not in self.data.columns:
                self.data['extreme_score'] = 0
        
        self.data['date'] = pd.to_datetime(self.data['date'])
        self.analysis_results = analysis_results
        
        # Add price_change if not present
        if 'price_change' not in self.data.columns:
            self.data['price_change'] = self.data['spx_close'].diff()
        
        # Set up matplotlib style
        plt.style.use('default')
        sns.set_palette("Set2")
        
    def create_price_and_greeks_chart(self) -> str:
        """Create comprehensive price and Greeks time series chart"""
        fig, axes = plt.subplots(4, 1, figsize=(15, 16))
        fig.suptitle('SPX Price and Options Greeks Analysis', fontsize=16, fontweight='bold')
        
        # Chart 1: SPX Price with key events
        ax1 = axes[0]
        ax1.plot(self.data['date'], self.data['spx_close'], linewidth=2, color='blue', label='SPX Close')
        
        # Mark convergence events
        if 'convergence_events' in self.analysis_results:
            convergence_events = self.analysis_results['convergence_events']
            if len(convergence_events) > 0:
                for _, event in convergence_events.iterrows():
                    color = 'red' if event['event_type'] == 'historic_convergence' else 'orange'
                    ax1.axvline(x=event['date'], color=color, linestyle='--', alpha=0.7, 
                               label=f"{event['event_type'].replace('_', ' ').title()}")
        
        ax1.set_title('SPX Price with Convergence Events')
        ax1.set_ylabel('SPX Price')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Chart 2: Vomma and Vanna
        ax2 = axes[1]
        ax2_twin = ax2.twinx()
        
        line1 = ax2.plot(self.data['date'], self.data['Vomma_M'], color='red', linewidth=2, label='Vomma (M)')
        line2 = ax2_twin.plot(self.data['date'], self.data['Vanna_K'], color='green', linewidth=2, label='Vanna (K)')
        
        ax2.set_title('Vomma and Vanna Over Time')
        ax2.set_ylabel('Vomma (Millions)', color='red')
        ax2_twin.set_ylabel('Vanna (Thousands)', color='green')
        ax2.tick_params(axis='y', labelcolor='red')
        ax2_twin.tick_params(axis='y', labelcolor='green')
        
        # Combine legends
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='upper left')
        ax2.grid(True, alpha=0.3)
        
        # Chart 3: Theta and Charm
        ax3 = axes[2]
        ax3_twin = ax3.twinx()
        
        line3 = ax3.plot(self.data['date'], self.data['Theta_M'], color='purple', linewidth=2, label='Theta (M)')
        line4 = ax3_twin.plot(self.data['date'], self.data['Charm_M'], color='brown', linewidth=2, label='Charm (M)')
        
        ax3.set_title('Theta and Charm Over Time')
        ax3.set_ylabel('Theta (Millions)', color='purple')
        ax3_twin.set_ylabel('Charm (Millions)', color='brown')
        ax3.tick_params(axis='y', labelcolor='purple')
        ax3_twin.tick_params(axis='y', labelcolor='brown')
        
        # Combine legends
        lines = line3 + line4
        labels = [l.get_label() for l in lines]
        ax3.legend(lines, labels, loc='upper left')
        ax3.grid(True, alpha=0.3)
        
        # Chart 4: Open Interest and Volume
        ax4 = axes[3]
        ax4_twin = ax4.twinx()
        
        line5 = ax4.bar(self.data['date'], self.data['Total_OI_K'], alpha=0.6, color='cyan', label='Open Interest (K)')
        line6 = ax4_twin.plot(self.data['date'], self.data['Volume'], color='orange', linewidth=2, label='Volume')
        
        ax4.set_title('Open Interest and Volume')
        ax4.set_ylabel('Open Interest (Thousands)', color='cyan')
        ax4_twin.set_ylabel('Volume', color='orange')
        ax4.tick_params(axis='y', labelcolor='cyan')
        ax4_twin.tick_params(axis='y', labelcolor='orange')
        ax4.set_xlabel('Date')
        
        # Format x-axis
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # Save chart
        chart_path = self.config.get_output_path('price_and_greeks_chart.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def create_convergence_analysis_chart(self) -> str:
        """Create convergence events analysis chart"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Convergence Events Analysis', fontsize=16, fontweight='bold')
        
        # Chart 1: Extreme Score over time
        ax1 = axes[0, 0]
        ax1.plot(self.data['date'], self.data['extreme_score'], linewidth=2, color='red', marker='o', markersize=4)
        ax1.axhline(y=4, color='orange', linestyle='--', alpha=0.7, label='Convergence Threshold')
        ax1.axhline(y=6, color='red', linestyle='--', alpha=0.7, label='Major Convergence')
        ax1.set_title('Extreme Score Timeline')
        ax1.set_ylabel('Extreme Score')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Chart 2: Vomma vs Price Change
        ax2 = axes[0, 1]
        scatter = ax2.scatter(self.data['price_change'], self.data['Vomma_M'], 
                             c=self.data['extreme_score'], cmap='Reds', alpha=0.7, s=50)
        ax2.set_title('Vomma vs Price Change')
        ax2.set_xlabel('Price Change')
        ax2.set_ylabel('Vomma (M)')
        ax2.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax2, label='Extreme Score')
        
        # Chart 3: Delta P/C Ratio
        ax3 = axes[1, 0]
        ax3.plot(self.data['date'], self.data['Delta_PC_Ratio'], linewidth=2, color='green')
        ax3.axhline(y=0.5, color='blue', linestyle='--', alpha=0.7, label='Bullish Threshold')
        ax3.axhline(y=1.5, color='red', linestyle='--', alpha=0.7, label='Bearish Threshold')
        ax3.set_title('Delta Put/Call Ratio')
        ax3.set_ylabel('Delta P/C Ratio')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Chart 4: Volume vs Extreme Events
        ax4 = axes[1, 1]
        colors = ['red' if score >= 6 else 'orange' if score >= 4 else 'blue' 
                 for score in self.data['extreme_score']]
        ax4.scatter(self.data['Volume'], self.data['Total_OI_K'], c=colors, alpha=0.7, s=50)
        ax4.set_title('Volume vs Open Interest (colored by extreme score)')
        ax4.set_xlabel('Volume')
        ax4.set_ylabel('Open Interest (K)')
        ax4.grid(True, alpha=0.3)
        
        # Format x-axis for time series
        for ax in [ax1, ax3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # Save chart
        chart_path = self.config.get_output_path('convergence_analysis_chart.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def create_correlation_heatmap(self) -> str:
        """Create correlation heatmap for Greeks and price"""
        if 'correlations' not in self.analysis_results:
            return None
            
        correlation_matrix = self.analysis_results['correlations']['correlation_matrix']
        
        # Select key variables for heatmap
        key_vars = ['spx_close', 'Vomma_M', 'Vanna_K', 'Charm_M', 'Theta_M', 
                   'Delta_M', 'Gamma_M', 'Vega_M', 'Total_OI_K', 'Volume', 'Delta_PC_Ratio']
        
        # Filter correlation matrix
        filtered_corr = correlation_matrix.loc[key_vars, key_vars]
        
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # Create heatmap
        sns.heatmap(filtered_corr, annot=True, cmap='RdBu_r', center=0, 
                   square=True, fmt='.3f', cbar_kws={'label': 'Correlation'})
        
        plt.title('Greeks and Price Correlation Matrix', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # Save chart
        chart_path = self.config.get_output_path('correlation_heatmap.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def create_regime_analysis_chart(self) -> str:
        """Create market regime analysis chart"""
        if 'regimes' not in self.analysis_results:
            return None
            
        regime_data = self.analysis_results['regimes']['regime_data']
        
        fig, axes = plt.subplots(2, 1, figsize=(15, 10))
        fig.suptitle('Market Regime Analysis', fontsize=16, fontweight='bold')
        
        # Chart 1: Price with Greek regimes
        ax1 = axes[0]
        
        # Color code by Greek regime
        regime_colors = {'normal': 'blue', 'extreme': 'red', 'distribution': 'orange', 'collapse': 'purple'}
        
        for regime in regime_data['greek_regime'].unique():
            regime_subset = regime_data[regime_data['greek_regime'] == regime]
            ax1.scatter(regime_subset['date'], regime_subset['spx_close'], 
                       c=regime_colors.get(regime, 'gray'), label=f'{regime.title()} Regime', 
                       alpha=0.7, s=30)
        
        ax1.set_title('SPX Price by Greek Regime')
        ax1.set_ylabel('SPX Price')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Chart 2: Volatility regimes
        ax2 = axes[1]
        
        vol_colors = {'low_vol': 'green', 'normal': 'blue', 'high_vol': 'red'}
        
        for regime in regime_data['volatility_regime'].unique():
            regime_subset = regime_data[regime_data['volatility_regime'] == regime]
            ax2.scatter(regime_subset['date'], regime_subset['spx_close'], 
                       c=vol_colors.get(regime, 'gray'), label=f'{regime.replace("_", " ").title()}', 
                       alpha=0.7, s=30)
        
        ax2.set_title('SPX Price by Volatility Regime')
        ax2.set_ylabel('SPX Price')
        ax2.set_xlabel('Date')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Format x-axis
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # Save chart
        chart_path = self.config.get_output_path('regime_analysis_chart.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.chart_paths.append(chart_path)
        return str(chart_path)
    
    def generate_markdown_report(self) -> str:
        """Generate comprehensive markdown report"""
        report_path = self.config.get_output_path('spx_greeks_analysis_report.md')
        
        # Start with title and summary
        with open(report_path, 'w') as f:
            f.write(f"# SPX Options Greeks Analysis Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**Analysis Period:** {self.data['date'].min().strftime('%Y-%m-%d')} to {self.data['date'].max().strftime('%Y-%m-%d')}\n\n")
            f.write(f"**Total Days Analyzed:** {len(self.data)}\n\n")
            f.write("---\n\n")
        
        # Executive Summary
        self._append_executive_summary(report_path)
        
        # Key Findings
        self._append_key_findings(report_path)
        
        # Convergence Events Analysis
        self._append_convergence_analysis(report_path)
        
        # Vomma/Price Divergence Analysis
        self._append_divergence_analysis(report_path)
        
        # Market Regime Analysis
        self._append_regime_analysis(report_path)
        
        # Charts section
        self._append_charts_section(report_path)
        
        # Technical Details
        self._append_technical_details(report_path)
        
        return str(report_path)
    
    def _append_executive_summary(self, report_path: str):
        """Append executive summary section"""
        summary = self.analysis_results.get('summary', {})
        key_findings = summary.get('key_findings', {})
        
        content = f"""## Executive Summary

This analysis examines SPX options Greeks patterns from {self.data['date'].min().strftime('%B %Y')} to {self.data['date'].max().strftime('%B %Y')}, focusing on extreme events and convergence patterns that indicate potential market manipulation or engineered moves.

### Key Metrics:
- **Total Convergence Events:** {key_findings.get('total_convergence_events', 0)}
- **Distribution Signals:** {key_findings.get('distribution_signals', 0)}
- **Volume Collapse Events:** {key_findings.get('volume_collapse_events', 0)}
- **Extreme Greek Days:** {key_findings.get('extreme_greek_days', 0)}

### Market Behavior:
The analysis reveals {len(self.data[self.data['extreme_score'] >= 4])} days with extreme Greek positioning, suggesting periods of heightened institutional activity and potential market engineering.

"""
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_key_findings(self, report_path: str):
        """Append key findings section"""
        content = """## Key Findings

### 1. Convergence Events
"""
        
        if 'convergence_events' in self.analysis_results:
            convergence_events = self.analysis_results['convergence_events']
            if len(convergence_events) > 0:
                content += f"Identified {len(convergence_events)} convergence events where multiple Greeks reached extreme levels simultaneously:\n\n"
                
                for _, event in convergence_events.iterrows():
                    content += f"- **{event['date'].strftime('%Y-%m-%d')}**: {event['event_type'].replace('_', ' ').title()} "
                    content += f"(Score: {event['extreme_score']}, SPX: {event['spx_close']:.2f})\n"
                
                content += "\n"
            else:
                content += "No significant convergence events detected in this period.\n\n"
        
        content += """### 2. Vomma/Price Divergence Patterns
"""
        
        if 'vomma_divergence' in self.analysis_results:
            divergence = self.analysis_results['vomma_divergence']
            content += f"- Distribution signals detected: {len(divergence['distribution_signals'])}\n"
            content += f"- Acceleration signals detected: {len(divergence['acceleration_signals'])}\n"
            content += f"- Vomma-Price correlation: {divergence['correlation_vomma_price']:.3f}\n\n"
        
        content += """### 3. Market Regime Classification
"""
        
        if 'regimes' in self.analysis_results:
            regimes = self.analysis_results['regimes']
            content += "**Volatility Regimes:**\n"
            for regime, count in regimes['volatility_regimes'].items():
                content += f"- {regime.replace('_', ' ').title()}: {count} days\n"
            
            content += "\n**Greek Regimes:**\n"
            for regime, count in regimes['greek_regimes'].items():
                content += f"- {regime.replace('_', ' ').title()}: {count} days\n"
            content += "\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_convergence_analysis(self, report_path: str):
        """Append detailed convergence analysis"""
        content = """## Convergence Events Analysis

Convergence events occur when multiple Greeks reach extreme levels simultaneously, often indicating institutional positioning or market engineering. These events are scored based on the number and magnitude of extreme Greek values.

### Scoring System:
- **Extreme Vomma (>50M):** +2 points
- **Extreme Vanna (>300K):** +2 points  
- **High Open Interest (>1M):** +1 point
- **Extreme Charm (>10M):** +1 point
- **High Theta (>100M):** +1 point
- **Extreme Delta P/C Ratio:** +1 point

### Event Classifications:
- **Convergence (Score 4-5):** Multiple extremes present
- **Major Convergence (Score 6-7):** Significant institutional activity
- **Historic Convergence (Score 8+):** Unprecedented positioning

"""
        
        if 'convergence_events' in self.analysis_results:
            convergence_events = self.analysis_results['convergence_events']
            if len(convergence_events) > 0:
                content += "### Detailed Event Analysis:\n\n"
                
                for _, event in convergence_events.iterrows():
                    content += f"#### {event['date'].strftime('%Y-%m-%d')} - {event['event_type'].replace('_', ' ').title()}\n"
                    content += f"- **Extreme Score:** {event['extreme_score']}\n"
                    content += f"- **SPX Price:** {event['spx_close']:.2f}\n"
                    content += f"- **Vomma:** {event['Vomma_M']:.2f}M\n"
                    content += f"- **Vanna:** {event['Vanna_K']:.2f}K\n"
                    content += f"- **Open Interest:** {event['Total_OI_K']:.2f}K\n"
                    content += f"- **Delta P/C Ratio:** {event['Delta_PC_Ratio']:.3f}\n\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_divergence_analysis(self, report_path: str):
        """Append Vomma/Price divergence analysis"""
        content = """## Vomma/Price Divergence Analysis

Vomma (volatility of volatility) divergence from price movements can signal distribution or acceleration patterns:

### Distribution Pattern:
- Price rises while Vomma explodes higher
- Indicates institutional distribution into retail buying
- Creates perfect trap for late buyers

### Acceleration Pattern:  
- Price falls with negative Vomma
- Creates self-reinforcing decline
- Amplifies volatility impact

"""
        
        if 'vomma_divergence' in self.analysis_results:
            divergence = self.analysis_results['vomma_divergence']
            
            content += f"### Analysis Results:\n"
            content += f"- **Vomma-Price Correlation:** {divergence['correlation_vomma_price']:.3f}\n"
            content += f"- **Distribution Signals:** {len(divergence['distribution_signals'])}\n"
            content += f"- **Acceleration Signals:** {len(divergence['acceleration_signals'])}\n\n"
            
            if len(divergence['distribution_signals']) > 0:
                content += "### Distribution Signal Details:\n\n"
                for _, signal in divergence['distribution_signals'].head(3).iterrows():
                    content += f"- **{signal['date'].strftime('%Y-%m-%d')}**: Price +{signal['price_change']:.2f}, "
                    content += f"Vomma {signal['Vomma_M']:.2f}M (+{signal['vomma_change']:.2f}M)\n"
                content += "\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_regime_analysis(self, report_path: str):
        """Append market regime analysis"""
        content = """## Market Regime Analysis

Market regimes are classified based on volatility levels and Greek extremes:

### Volatility Regimes:
- **Low Vol:** Bottom 20% of Vega readings
- **Normal:** Middle 60% of Vega readings  
- **High Vol:** Top 20% of Vega readings

### Greek Regimes:
- **Normal:** Standard Greek levels
- **Extreme:** Multiple Greeks at extreme levels
- **Distribution:** Price up + high Greeks
- **Collapse:** Negative Vomma + price down

"""
        
        if 'regimes' in self.analysis_results:
            regimes = self.analysis_results['regimes']
            
            content += "### Regime Distribution:\n\n"
            content += "**Volatility Regimes:**\n"
            total_days = sum(regimes['volatility_regimes'].values())
            for regime, count in regimes['volatility_regimes'].items():
                pct = (count / total_days) * 100
                content += f"- {regime.replace('_', ' ').title()}: {count} days ({pct:.1f}%)\n"
            
            content += "\n**Greek Regimes:**\n"
            total_days = sum(regimes['greek_regimes'].values())
            for regime, count in regimes['greek_regimes'].items():
                pct = (count / total_days) * 100
                content += f"- {regime.replace('_', ' ').title()}: {count} days ({pct:.1f}%)\n"
            content += "\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_charts_section(self, report_path: str):
        """Append charts section"""
        content = """## Charts and Visualizations

The following charts provide visual analysis of the Greeks patterns and market behavior:

"""
        
        chart_descriptions = {
            'price_and_greeks_chart.png': 'SPX Price and Options Greeks Time Series',
            'convergence_analysis_chart.png': 'Convergence Events Analysis',
            'correlation_heatmap.png': 'Greeks and Price Correlation Matrix',
            'regime_analysis_chart.png': 'Market Regime Classification'
        }
        
        for chart_path in self.chart_paths:
            chart_name = Path(chart_path).name
            if chart_name in chart_descriptions:
                content += f"### {chart_descriptions[chart_name]}\n\n"
                content += f"![{chart_descriptions[chart_name]}]({chart_name})\n\n"
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def _append_technical_details(self, report_path: str):
        """Append technical details section"""
        content = """## Technical Details

### Data Processing:
- **Source:** SPX options chain data Q2 2025
- **Greeks Calculated:** Delta, Gamma, Vega, Theta, Rho
- **Derived Greeks:** Vomma, Vanna, Charm
- **Aggregation:** Daily totals weighted by Open Interest

### Methodology:
- **Vomma Calculation:** Vega × Gamma × (Price/100)
- **Vanna Calculation:** Vega × Gamma / Price  
- **Charm Calculation:** -Theta × Gamma / Price
- **Extreme Thresholds:** Based on statistical analysis of historical data

### Risk Considerations:
- Analysis based on historical data patterns
- Market conditions can change rapidly
- Greeks calculations are approximations
- Past patterns may not predict future behavior

---

*This report was generated using the SPX Greeks Analysis Engine*
"""
        
        with open(report_path, 'a') as f:
            f.write(content)
    
    def generate_pdf_report(self) -> str:
        """Generate PDF report from markdown"""
        # First generate all charts
        print("Generating charts...")
        self.create_price_and_greeks_chart()
        self.create_convergence_analysis_chart()
        self.create_correlation_heatmap()
        self.create_regime_analysis_chart()
        
        # Generate markdown report
        print("Generating markdown report...")
        md_path = self.generate_markdown_report()
        
        # Convert to PDF using manus utility
        print("Converting to PDF...")
        pdf_path = self.config.get_output_path('spx_greeks_analysis_report.pdf')
        
        # Use the manus-md-to-pdf utility
        import subprocess
        try:
            result = subprocess.run([
                'manus-md-to-pdf', 
                str(md_path), 
                str(pdf_path)
            ], capture_output=True, text=True, cwd=str(self.config.REPORTS_DIR))
            
            if result.returncode == 0:
                print(f"PDF report generated successfully: {pdf_path}")
                return str(pdf_path)
            else:
                print(f"Error generating PDF: {result.stderr}")
                return str(md_path)  # Return markdown if PDF fails
                
        except Exception as e:
            print(f"Error running manus-md-to-pdf: {e}")
            return str(md_path)  # Return markdown if PDF fails
    
    def get_chart_paths(self) -> List[str]:
        """Get list of generated chart paths"""
        return self.chart_paths

