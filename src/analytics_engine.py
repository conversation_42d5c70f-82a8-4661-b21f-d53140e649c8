"""
Analytics Engine for SPX Options Greeks Analysis
Implements pattern recognition and extreme event analysis
"""
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class GreeksAnalyticsEngine:
    """Analytics engine for Greeks pattern recognition and analysis"""
    
    def __init__(self, config):
        self.config = config
        self.data = None
        self.analysis_results = {}
        
    def set_data(self, daily_aggregates: pd.DataFrame):
        """Set the daily aggregated data for analysis"""
        self.data = daily_aggregates.copy()
        self.data['date'] = pd.to_datetime(self.data['date'])
        self.data = self.data.sort_values('date').reset_index(drop=True)
        
    def identify_convergence_events(self) -> pd.DataFrame:
        """
        Identify convergence events where multiple Greeks reach extreme levels using data-driven thresholds.

        Convergence events occur when multiple Greeks reach extreme levels simultaneously,
        indicating potential market stress or significant price movements.

        The scoring system assigns points based on extreme values:
        - Vomma/Vanna: 3 points each (most important for volatility)
        - Theta/Charm: 2 points each (time decay indicators)
        - Open Interest/Delta P/C: 1 point each (positioning indicators)

        Event classification:
        - Convergence: 6+ points (moderate stress)
        - Major Convergence: 9+ points (significant stress)
        - Historic Convergence: 12+ points (extreme stress)

        Returns:
            pd.DataFrame: DataFrame containing convergence events with dates, scores, and classifications
        """
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")

        # Get configuration constants for data-driven analysis
        config = self.config.DATA_DRIVEN_CONFIG
        extreme_pct = config['extreme_percentile']  # 90th percentile for extreme events

        # Calculate data-driven extreme thresholds using percentiles
        # This approach adapts to actual data distribution rather than using arbitrary fixed values
        vomma_threshold = abs(self.data['Vomma_M']).quantile(extreme_pct)
        vanna_threshold = abs(self.data['Vanna_K']).quantile(extreme_pct)
        charm_threshold = abs(self.data['Charm_M']).quantile(extreme_pct)
        theta_threshold = abs(self.data['Theta_M']).quantile(extreme_pct)
        oi_threshold = self.data['Total_OI_K'].quantile(extreme_pct)

        # Calculate Delta P/C ratio extremes (bullish and bearish positioning)
        # Low values = bullish institutional positioning, High values = bearish positioning
        delta_pc_low = self.data['Delta_PC_Ratio'].quantile(1 - extreme_pct)  # Extremely bullish (10th percentile)
        delta_pc_high = self.data['Delta_PC_Ratio'].quantile(extreme_pct)     # Extremely bearish (90th percentile)

        print(f"Data-driven convergence thresholds:")
        print(f"  Vomma: {vomma_threshold:.1f}M")
        print(f"  Vanna: {vanna_threshold:.1f}K")
        print(f"  Charm: {charm_threshold:.1f}M")
        print(f"  Theta: {theta_threshold:.1f}M")
        print(f"  Open Interest: {oi_threshold:.1f}K")
        print(f"  Delta P/C Ratio: {delta_pc_low:.3f} (bullish) / {delta_pc_high:.3f} (bearish)")

        # Create scoring system for convergence events
        self.data['extreme_score'] = 0

        # Score each extreme condition using configurable weights from config
        # Higher weights indicate more significant impact on market behavior
        self.data.loc[abs(self.data['Vomma_M']) > vomma_threshold, 'extreme_score'] += config['vomma_score_weight']
        self.data.loc[abs(self.data['Vanna_K']) > vanna_threshold, 'extreme_score'] += config['vanna_score_weight']
        self.data.loc[abs(self.data['Charm_M']) > charm_threshold, 'extreme_score'] += config['charm_score_weight']
        self.data.loc[abs(self.data['Theta_M']) > theta_threshold, 'extreme_score'] += config['theta_score_weight']
        self.data.loc[self.data['Total_OI_K'] > oi_threshold, 'extreme_score'] += config['oi_score_weight']

        # Add points for extreme Delta P/C positioning (both bullish and bearish extremes)
        self.data.loc[self.data['Delta_PC_Ratio'] < delta_pc_low, 'extreme_score'] += config['delta_pc_score_weight']
        self.data.loc[self.data['Delta_PC_Ratio'] > delta_pc_high, 'extreme_score'] += config['delta_pc_score_weight']

        # Identify convergence events using configurable scoring thresholds
        min_score = config['convergence_min_score']  # Minimum score for convergence event
        convergence_events = self.data[self.data['extreme_score'] >= min_score].copy()

        # Add event classification using configurable thresholds
        convergence_events['event_type'] = 'convergence'
        convergence_events.loc[convergence_events['extreme_score'] >= config['major_convergence_min_score'], 'event_type'] = 'major_convergence'
        convergence_events.loc[convergence_events['extreme_score'] >= config['historic_convergence_min_score'], 'event_type'] = 'historic_convergence'

        # Store thresholds for reporting
        self.analysis_results['convergence_thresholds'] = {
            'vomma_threshold': vomma_threshold,
            'vanna_threshold': vanna_threshold,
            'charm_threshold': charm_threshold,
            'theta_threshold': theta_threshold,
            'oi_threshold': oi_threshold,
            'delta_pc_low': delta_pc_low,
            'delta_pc_high': delta_pc_high
        }

        self.analysis_results['convergence_events'] = convergence_events

        print(f"Identified {len(convergence_events)} convergence events")
        print(f"  Major convergences: {len(convergence_events[convergence_events['event_type'] == 'major_convergence'])}")
        print(f"  Historic convergences: {len(convergence_events[convergence_events['event_type'] == 'historic_convergence'])}")

        return convergence_events
    
    def analyze_vomma_price_divergence(self) -> Dict:
        """
        Analyze Vomma/Price divergence patterns as described in documents.

        Divergence occurs when price movements don't align with Vomma movements,
        potentially indicating:
        - Market inefficiencies
        - Upcoming volatility changes
        - Options positioning imbalances

        Patterns analyzed:
        - Price up + Vomma down (bearish divergence)
        - Price down + Vomma up (bullish divergence)
        - Large Vomma changes without corresponding price moves

        Returns:
            Dict: Dictionary containing divergence statistics and patterns
        """
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")

        # Calculate price changes using configuration constants
        config = self.config.DATA_DRIVEN_CONFIG
        percentage_multiplier = config['percentage_multiplier']

        self.data['price_change'] = self.data['spx_close'].diff()
        self.data['price_change_pct'] = self.data['spx_close'].pct_change() * percentage_multiplier

        # Calculate Vomma changes
        self.data['vomma_change'] = self.data['Vomma_M'].diff()
        self.data['vomma_change_pct'] = self.data['Vomma_M'].pct_change() * percentage_multiplier

        # Use data-driven thresholds for divergence patterns
        medium_high_pct = config['medium_high_percentile']  # 75th percentile
        low_pct = config['low_percentile']                  # 25th percentile

        vomma_high_threshold = self.data['Vomma_M'].quantile(medium_high_pct)  # 75th percentile for high Vomma
        vomma_low_threshold = self.data['Vomma_M'].quantile(low_pct)           # 25th percentile for low Vomma
        vomma_change_threshold = abs(self.data['vomma_change']).quantile(medium_high_pct)  # 75th percentile for large changes

        # Identify divergence patterns
        # Pattern 1: Price rises while Vomma explodes (distribution signal)
        distribution_pattern = (
            (self.data['price_change'] > 0) &  # Price rising
            (self.data['Vomma_M'] > vomma_high_threshold) &  # High Vomma (data-driven)
            (self.data['vomma_change'] > vomma_change_threshold)  # Vomma increasing significantly
        )

        # Pattern 2: Negative Vomma with price decline (acceleration pattern)
        acceleration_pattern = (
            (self.data['price_change'] < 0) &  # Price falling
            (self.data['Vomma_M'] < vomma_low_threshold) &  # Low/Negative Vomma (data-driven)
            (abs(self.data['vomma_change']) > vomma_change_threshold)  # Large Vomma change
        )
        
        divergence_analysis = {
            'distribution_signals': self.data[distribution_pattern].copy(),
            'acceleration_signals': self.data[acceleration_pattern].copy(),
            'correlation_vomma_price': self.data['Vomma_M'].corr(self.data['spx_close']),
            'correlation_vomma_price_change': self.data['Vomma_M'].corr(self.data['price_change'])
        }
        
        self.analysis_results['vomma_divergence'] = divergence_analysis
        
        print(f"Vomma/Price Divergence Analysis:")
        print(f"  Distribution signals: {len(divergence_analysis['distribution_signals'])}")
        print(f"  Acceleration signals: {len(divergence_analysis['acceleration_signals'])}")
        print(f"  Vomma-Price correlation: {divergence_analysis['correlation_vomma_price']:.3f}")
        
        return divergence_analysis
    
    def analyze_theta_decay_urgency(self) -> Dict:
        """Analyze Theta decay patterns and urgency signals"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Calculate Theta changes and patterns using configuration constants
        config = self.config.DATA_DRIVEN_CONFIG
        percentage_multiplier = config['percentage_multiplier']

        self.data['theta_change'] = self.data['Theta_M'].diff()
        self.data['theta_change_pct'] = self.data['Theta_M'].pct_change() * percentage_multiplier

        # Use configurable data-driven thresholds for theta analysis
        extreme_pct = config['extreme_percentile']  # 90th percentile
        low_pct = config['low_percentile']          # 25th percentile

        theta_change_threshold = self.data['theta_change'].quantile(extreme_pct)  # Large theta changes
        theta_low_threshold = abs(self.data['Theta_M']).quantile(low_pct)         # Low theta values
        theta_high_threshold = abs(self.data['Theta_M']).quantile(extreme_pct)    # High theta values
        vomma_high_threshold = abs(self.data['Vomma_M']).quantile(extreme_pct)    # High vomma values

        # Identify urgency patterns
        # Pattern 1: Massive Theta collapse (urgency to exit)
        theta_collapse = (
            (self.data['theta_change'] > theta_change_threshold) &  # Large positive change (less negative)
            (abs(self.data['Theta_M']) < theta_low_threshold)  # Low absolute theta (near zero)
        )

        # Pattern 2: High Theta with high Vomma (dangerous combination)
        dangerous_combo = (
            (abs(self.data['Theta_M']) > theta_high_threshold) &  # High Theta (data-driven)
            (abs(self.data['Vomma_M']) > vomma_high_threshold)  # High Vomma (data-driven)
        )
        
        theta_analysis = {
            'theta_collapse_events': self.data[theta_collapse].copy(),
            'dangerous_combinations': self.data[dangerous_combo].copy(),
            'theta_statistics': {
                'mean': self.data['Theta_M'].mean(),
                'std': self.data['Theta_M'].std(),
                'min': self.data['Theta_M'].min(),
                'max': self.data['Theta_M'].max()
            }
        }
        
        self.analysis_results['theta_analysis'] = theta_analysis
        
        print(f"Theta Decay Analysis:")
        print(f"  Theta collapse events: {len(theta_analysis['theta_collapse_events'])}")
        print(f"  Dangerous combinations: {len(theta_analysis['dangerous_combinations'])}")
        
        return theta_analysis
    
    def analyze_volume_patterns(self) -> Dict:
        """Analyze volume collapse patterns after extreme events"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Calculate volume changes using configuration constants
        config = self.config.DATA_DRIVEN_CONFIG
        percentage_multiplier = config['percentage_multiplier']

        self.data['volume_change'] = self.data['Volume'].diff()
        self.data['volume_change_pct'] = self.data['Volume'].pct_change() * percentage_multiplier

        # Calculate rolling volume averages using configuration constants
        short_period = config['volume_ma_short_period']  # 5 days
        long_period = config['volume_ma_long_period']    # 10 days

        self.data['volume_ma_5'] = self.data['Volume'].rolling(short_period).mean()
        self.data['volume_ma_10'] = self.data['Volume'].rolling(long_period).mean()

        # Identify volume collapse events using configuration constants
        collapse_threshold = config['volume_collapse_threshold']  # -50%
        volume_collapse = (
            (self.data['volume_change_pct'] < collapse_threshold) &  # >50% volume drop
            (self.data['Volume'] > 0)  # Valid volume data
        )

        # Identify volume spikes using configuration constants
        spike_threshold = config['volume_spike_threshold']    # 100%
        volume_multiplier = config['volume_ma_multiplier']    # 2x
        volume_spike = (
            (self.data['volume_change_pct'] > spike_threshold) &  # >100% volume increase
            (self.data['Volume'] > self.data['volume_ma_10'] * volume_multiplier)  # Above 2x average
        )
        
        volume_analysis = {
            'volume_collapse_events': self.data[volume_collapse].copy(),
            'volume_spike_events': self.data[volume_spike].copy(),
            'volume_statistics': {
                'mean': self.data['Volume'].mean(),
                'median': self.data['Volume'].median(),
                'std': self.data['Volume'].std()
            }
        }
        
        self.analysis_results['volume_analysis'] = volume_analysis
        
        print(f"Volume Pattern Analysis:")
        print(f"  Volume collapse events: {len(volume_analysis['volume_collapse_events'])}")
        print(f"  Volume spike events: {len(volume_analysis['volume_spike_events'])}")
        
        return volume_analysis
    
    def calculate_greek_correlations(self) -> Dict:
        """Calculate correlations between different Greeks and price"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Select Greek columns for correlation analysis
        greek_columns = ['Vomma_M', 'Vanna_K', 'Charm_M', 'Theta_M', 'Delta_M', 'Gamma_M', 'Vega_M']
        price_columns = ['spx_close', 'spx_high', 'spx_low']
        
        # Calculate correlation matrix
        correlation_data = self.data[greek_columns + price_columns + ['Total_OI_K', 'Volume', 'Delta_PC_Ratio']]
        correlation_matrix = correlation_data.corr()
        
        # Extract key correlations
        key_correlations = {
            'vomma_price': correlation_matrix.loc['Vomma_M', 'spx_close'],
            'vanna_price': correlation_matrix.loc['Vanna_K', 'spx_close'],
            'charm_price': correlation_matrix.loc['Charm_M', 'spx_close'],
            'theta_price': correlation_matrix.loc['Theta_M', 'spx_close'],
            'oi_price': correlation_matrix.loc['Total_OI_K', 'spx_close'],
            'volume_price': correlation_matrix.loc['Volume', 'spx_close'],
            'vomma_vanna': correlation_matrix.loc['Vomma_M', 'Vanna_K'],
            'vomma_oi': correlation_matrix.loc['Vomma_M', 'Total_OI_K'],
        }
        
        correlations_analysis = {
            'correlation_matrix': correlation_matrix,
            'key_correlations': key_correlations,
            'greek_columns': greek_columns,
            'price_columns': price_columns
        }
        
        self.analysis_results['correlations'] = correlations_analysis
        
        print(f"Greek Correlations Analysis:")
        print(f"  Vomma-Price correlation: {key_correlations['vomma_price']:.3f}")
        print(f"  Vanna-Price correlation: {key_correlations['vanna_price']:.3f}")
        print(f"  Vomma-Vanna correlation: {key_correlations['vomma_vanna']:.3f}")
        
        return correlations_analysis
    
    def identify_market_regimes(self) -> Dict:
        """Identify different market regimes based on Greeks patterns"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Calculate regime indicators
        self.data['volatility_regime'] = 'normal'
        self.data['greek_regime'] = 'normal'
        
        # Use configuration constants for regime thresholds
        config = self.config.DATA_DRIVEN_CONFIG
        vol_high_threshold = config['vol_regime_high_threshold']      # 0.80
        vol_low_threshold = config['vol_regime_low_threshold']        # 0.20
        extreme_threshold = config['greek_regime_extreme_threshold']  # 0.85
        distribution_threshold = config['greek_regime_distribution_threshold']  # 0.75
        collapse_threshold = config['greek_regime_collapse_threshold']  # 0.25

        # High volatility regime
        high_vol_threshold = self.data['Vega_M'].quantile(vol_high_threshold)
        self.data.loc[self.data['Vega_M'] > high_vol_threshold, 'volatility_regime'] = 'high_vol'

        # Low volatility regime
        low_vol_threshold = self.data['Vega_M'].quantile(vol_low_threshold)
        self.data.loc[self.data['Vega_M'] < low_vol_threshold, 'volatility_regime'] = 'low_vol'

        # Use data-driven thresholds for regime classification
        vomma_extreme_threshold = abs(self.data['Vomma_M']).quantile(extreme_threshold)
        vanna_extreme_threshold = abs(self.data['Vanna_K']).quantile(extreme_threshold)
        oi_extreme_threshold = self.data['Total_OI_K'].quantile(extreme_threshold)
        vomma_distribution_threshold = self.data['Vomma_M'].quantile(distribution_threshold)
        vanna_distribution_threshold = self.data['Vanna_K'].quantile(distribution_threshold)
        vomma_collapse_threshold = self.data['Vomma_M'].quantile(collapse_threshold)

        # Extreme Greeks regime (using configuration threshold)
        extreme_greek_mask = (
            (abs(self.data['Vomma_M']) > vomma_extreme_threshold) |
            (abs(self.data['Vanna_K']) > vanna_extreme_threshold) |
            (self.data['Total_OI_K'] > oi_extreme_threshold)
        )
        self.data.loc[extreme_greek_mask, 'greek_regime'] = 'extreme'

        # Distribution regime (price up, high Greeks - using configuration threshold)
        distribution_mask = (
            (self.data['price_change'] > 0) &
            (self.data['Vomma_M'] > vomma_distribution_threshold) &
            (self.data['Vanna_K'] > vanna_distribution_threshold)
        )
        self.data.loc[distribution_mask, 'greek_regime'] = 'distribution'

        # Collapse regime (low Vomma, price down - using configuration threshold)
        collapse_mask = (
            (self.data['Vomma_M'] < vomma_collapse_threshold) &
            (self.data['price_change'] < 0)
        )
        self.data.loc[collapse_mask, 'greek_regime'] = 'collapse'
        
        regime_analysis = {
            'volatility_regimes': self.data['volatility_regime'].value_counts().to_dict(),
            'greek_regimes': self.data['greek_regime'].value_counts().to_dict(),
            'regime_data': self.data[['date', 'volatility_regime', 'greek_regime', 'spx_close']].copy()
        }
        
        self.analysis_results['regimes'] = regime_analysis
        
        print(f"Market Regime Analysis:")
        print(f"  Volatility regimes: {regime_analysis['volatility_regimes']}")
        print(f"  Greek regimes: {regime_analysis['greek_regimes']}")
        
        return regime_analysis
    
    def run_full_analysis(self) -> Dict:
        """Run complete analysis pipeline"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        print("Running full Greeks analysis...")
        
        # Run all analysis components
        self.identify_convergence_events()
        self.analyze_vomma_price_divergence()
        self.analyze_theta_decay_urgency()
        self.analyze_volume_patterns()
        self.calculate_greek_correlations()
        self.identify_market_regimes()
        
        # Generate summary
        summary = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_period': {
                'start': self.data['date'].min().strftime('%Y-%m-%d'),
                'end': self.data['date'].max().strftime('%Y-%m-%d'),
                'total_days': len(self.data)
            },
            'key_findings': {
                'total_convergence_events': len(self.analysis_results['convergence_events']),
                'distribution_signals': len(self.analysis_results['vomma_divergence']['distribution_signals']),
                'volume_collapse_events': len(self.analysis_results['volume_analysis']['volume_collapse_events']),
                'extreme_greek_days': len(self.data[self.data['extreme_score'] >= 4])
            }
        }
        
        self.analysis_results['summary'] = summary
        
        print("Analysis complete!")
        print(f"Key findings:")
        for key, value in summary['key_findings'].items():
            print(f"  {key}: {value}")
        
        return self.analysis_results
    
    def get_analysis_results(self) -> Dict:
        """Get all analysis results including processed data with extreme scores"""
        # Ensure the main data includes extreme_score
        if hasattr(self, 'data') and 'extreme_score' in self.data.columns:
            self.analysis_results['processed_data'] = self.data.copy()

        return self.analysis_results

    def add_raw_data_sample(self, data_loader):
        """Add raw data sample for expiry analysis charts"""
        if hasattr(data_loader, 'get_raw_data_sample'):
            self.analysis_results['raw_data_sample'] = data_loader.get_raw_data_sample()
        return self.analysis_results

