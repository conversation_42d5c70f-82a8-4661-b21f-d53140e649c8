"""
Analytics Engine for SPX Options Greeks Analysis
Implements pattern recognition and extreme event analysis
"""
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class GreeksAnalyticsEngine:
    """Analytics engine for Greeks pattern recognition and analysis"""
    
    def __init__(self, config):
        self.config = config
        self.data = None
        self.analysis_results = {}
        
    def set_data(self, daily_aggregates: pd.DataFrame):
        """Set the daily aggregated data for analysis"""
        self.data = daily_aggregates.copy()
        self.data['date'] = pd.to_datetime(self.data['date'])
        self.data = self.data.sort_values('date').reset_index(drop=True)
        
    def identify_convergence_events(self) -> pd.DataFrame:
        """Identify convergence events where multiple Greeks reach extreme levels"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        params = self.config.ANALYSIS_PARAMS
        
        # Define extreme thresholds (adjusted for our data scale)
        vomma_threshold = 50  # 50M in our millions scale
        vanna_threshold = 300  # 300K in our thousands scale  
        oi_threshold = 1000  # 1M in our thousands scale
        charm_threshold = 10  # 10M in our millions scale
        
        # Create scoring system for convergence events
        self.data['extreme_score'] = 0
        
        # Score each extreme condition
        self.data.loc[abs(self.data['Vomma_M']) > vomma_threshold, 'extreme_score'] += 2
        self.data.loc[abs(self.data['Vanna_K']) > vanna_threshold, 'extreme_score'] += 2
        self.data.loc[self.data['Total_OI_K'] > oi_threshold, 'extreme_score'] += 1
        self.data.loc[abs(self.data['Charm_M']) > charm_threshold, 'extreme_score'] += 1
        self.data.loc[abs(self.data['Theta_M']) > 100, 'extreme_score'] += 1  # 100M threshold
        self.data.loc[self.data['Delta_PC_Ratio'] < 0.4, 'extreme_score'] += 1  # Bullish delta positioning
        self.data.loc[self.data['Delta_PC_Ratio'] > 1.5, 'extreme_score'] += 1  # Bearish delta positioning
        
        # Identify convergence events (score >= 4 indicates multiple extremes)
        convergence_events = self.data[self.data['extreme_score'] >= 4].copy()
        
        # Add event classification
        convergence_events['event_type'] = 'convergence'
        convergence_events.loc[convergence_events['extreme_score'] >= 6, 'event_type'] = 'major_convergence'
        convergence_events.loc[convergence_events['extreme_score'] >= 8, 'event_type'] = 'historic_convergence'
        
        self.analysis_results['convergence_events'] = convergence_events
        
        print(f"Identified {len(convergence_events)} convergence events")
        print(f"  Major convergences: {len(convergence_events[convergence_events['event_type'] == 'major_convergence'])}")
        print(f"  Historic convergences: {len(convergence_events[convergence_events['event_type'] == 'historic_convergence'])}")
        
        return convergence_events
    
    def analyze_vomma_price_divergence(self) -> Dict:
        """Analyze Vomma/Price divergence patterns as described in documents"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Calculate price changes
        self.data['price_change'] = self.data['spx_close'].diff()
        self.data['price_change_pct'] = self.data['spx_close'].pct_change() * 100
        
        # Calculate Vomma changes
        self.data['vomma_change'] = self.data['Vomma_M'].diff()
        self.data['vomma_change_pct'] = self.data['Vomma_M'].pct_change() * 100
        
        # Identify divergence patterns
        # Pattern 1: Price rises while Vomma explodes (distribution signal)
        distribution_pattern = (
            (self.data['price_change'] > 0) &  # Price rising
            (self.data['Vomma_M'] > 50) &  # High Vomma
            (self.data['vomma_change'] > 20)  # Vomma increasing
        )
        
        # Pattern 2: Negative Vomma with price decline (acceleration pattern)
        acceleration_pattern = (
            (self.data['price_change'] < 0) &  # Price falling
            (self.data['Vomma_M'] < -50) &  # Negative Vomma
            (abs(self.data['vomma_change']) > 20)  # Large Vomma change
        )
        
        divergence_analysis = {
            'distribution_signals': self.data[distribution_pattern].copy(),
            'acceleration_signals': self.data[acceleration_pattern].copy(),
            'correlation_vomma_price': self.data['Vomma_M'].corr(self.data['spx_close']),
            'correlation_vomma_price_change': self.data['Vomma_M'].corr(self.data['price_change'])
        }
        
        self.analysis_results['vomma_divergence'] = divergence_analysis
        
        print(f"Vomma/Price Divergence Analysis:")
        print(f"  Distribution signals: {len(divergence_analysis['distribution_signals'])}")
        print(f"  Acceleration signals: {len(divergence_analysis['acceleration_signals'])}")
        print(f"  Vomma-Price correlation: {divergence_analysis['correlation_vomma_price']:.3f}")
        
        return divergence_analysis
    
    def analyze_theta_decay_urgency(self) -> Dict:
        """Analyze Theta decay patterns and urgency signals"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Calculate Theta changes and patterns
        self.data['theta_change'] = self.data['Theta_M'].diff()
        self.data['theta_change_pct'] = self.data['Theta_M'].pct_change() * 100
        
        # Identify urgency patterns
        # Pattern 1: Massive Theta collapse (urgency to exit)
        theta_collapse = (
            (self.data['theta_change'] > 50) &  # Large positive change (less negative)
            (abs(self.data['Theta_M']) < 50)  # Low absolute theta (near zero)
        )
        
        # Pattern 2: High Theta with high Vomma (dangerous combination)
        dangerous_combo = (
            (abs(self.data['Theta_M']) > 100) &  # High Theta
            (abs(self.data['Vomma_M']) > 50)  # High Vomma
        )
        
        theta_analysis = {
            'theta_collapse_events': self.data[theta_collapse].copy(),
            'dangerous_combinations': self.data[dangerous_combo].copy(),
            'theta_statistics': {
                'mean': self.data['Theta_M'].mean(),
                'std': self.data['Theta_M'].std(),
                'min': self.data['Theta_M'].min(),
                'max': self.data['Theta_M'].max()
            }
        }
        
        self.analysis_results['theta_analysis'] = theta_analysis
        
        print(f"Theta Decay Analysis:")
        print(f"  Theta collapse events: {len(theta_analysis['theta_collapse_events'])}")
        print(f"  Dangerous combinations: {len(theta_analysis['dangerous_combinations'])}")
        
        return theta_analysis
    
    def analyze_volume_patterns(self) -> Dict:
        """Analyze volume collapse patterns after extreme events"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Calculate volume changes
        self.data['volume_change'] = self.data['Volume'].diff()
        self.data['volume_change_pct'] = self.data['Volume'].pct_change() * 100
        
        # Calculate rolling volume averages
        self.data['volume_ma_5'] = self.data['Volume'].rolling(5).mean()
        self.data['volume_ma_10'] = self.data['Volume'].rolling(10).mean()
        
        # Identify volume collapse events (>50% drop)
        volume_collapse = (
            (self.data['volume_change_pct'] < -50) &  # >50% volume drop
            (self.data['Volume'] > 0)  # Valid volume data
        )
        
        # Identify volume spikes (>100% increase)
        volume_spike = (
            (self.data['volume_change_pct'] > 100) &  # >100% volume increase
            (self.data['Volume'] > self.data['volume_ma_10'] * 2)  # Above 2x average
        )
        
        volume_analysis = {
            'volume_collapse_events': self.data[volume_collapse].copy(),
            'volume_spike_events': self.data[volume_spike].copy(),
            'volume_statistics': {
                'mean': self.data['Volume'].mean(),
                'median': self.data['Volume'].median(),
                'std': self.data['Volume'].std()
            }
        }
        
        self.analysis_results['volume_analysis'] = volume_analysis
        
        print(f"Volume Pattern Analysis:")
        print(f"  Volume collapse events: {len(volume_analysis['volume_collapse_events'])}")
        print(f"  Volume spike events: {len(volume_analysis['volume_spike_events'])}")
        
        return volume_analysis
    
    def calculate_greek_correlations(self) -> Dict:
        """Calculate correlations between different Greeks and price"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Select Greek columns for correlation analysis
        greek_columns = ['Vomma_M', 'Vanna_K', 'Charm_M', 'Theta_M', 'Delta_M', 'Gamma_M', 'Vega_M']
        price_columns = ['spx_close', 'spx_high', 'spx_low']
        
        # Calculate correlation matrix
        correlation_data = self.data[greek_columns + price_columns + ['Total_OI_K', 'Volume', 'Delta_PC_Ratio']]
        correlation_matrix = correlation_data.corr()
        
        # Extract key correlations
        key_correlations = {
            'vomma_price': correlation_matrix.loc['Vomma_M', 'spx_close'],
            'vanna_price': correlation_matrix.loc['Vanna_K', 'spx_close'],
            'charm_price': correlation_matrix.loc['Charm_M', 'spx_close'],
            'theta_price': correlation_matrix.loc['Theta_M', 'spx_close'],
            'oi_price': correlation_matrix.loc['Total_OI_K', 'spx_close'],
            'volume_price': correlation_matrix.loc['Volume', 'spx_close'],
            'vomma_vanna': correlation_matrix.loc['Vomma_M', 'Vanna_K'],
            'vomma_oi': correlation_matrix.loc['Vomma_M', 'Total_OI_K'],
        }
        
        correlations_analysis = {
            'correlation_matrix': correlation_matrix,
            'key_correlations': key_correlations,
            'greek_columns': greek_columns,
            'price_columns': price_columns
        }
        
        self.analysis_results['correlations'] = correlations_analysis
        
        print(f"Greek Correlations Analysis:")
        print(f"  Vomma-Price correlation: {key_correlations['vomma_price']:.3f}")
        print(f"  Vanna-Price correlation: {key_correlations['vanna_price']:.3f}")
        print(f"  Vomma-Vanna correlation: {key_correlations['vomma_vanna']:.3f}")
        
        return correlations_analysis
    
    def identify_market_regimes(self) -> Dict:
        """Identify different market regimes based on Greeks patterns"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Calculate regime indicators
        self.data['volatility_regime'] = 'normal'
        self.data['greek_regime'] = 'normal'
        
        # High volatility regime
        high_vol_threshold = self.data['Vega_M'].quantile(0.8)
        self.data.loc[self.data['Vega_M'] > high_vol_threshold, 'volatility_regime'] = 'high_vol'
        
        # Low volatility regime  
        low_vol_threshold = self.data['Vega_M'].quantile(0.2)
        self.data.loc[self.data['Vega_M'] < low_vol_threshold, 'volatility_regime'] = 'low_vol'
        
        # Extreme Greeks regime
        extreme_greek_mask = (
            (abs(self.data['Vomma_M']) > 50) |
            (abs(self.data['Vanna_K']) > 300) |
            (self.data['Total_OI_K'] > 1000)
        )
        self.data.loc[extreme_greek_mask, 'greek_regime'] = 'extreme'
        
        # Distribution regime (price up, high Greeks)
        distribution_mask = (
            (self.data['price_change'] > 0) &
            (self.data['Vomma_M'] > 30) &
            (self.data['Vanna_K'] > 200)
        )
        self.data.loc[distribution_mask, 'greek_regime'] = 'distribution'
        
        # Collapse regime (negative Vomma, price down)
        collapse_mask = (
            (self.data['Vomma_M'] < -30) &
            (self.data['price_change'] < 0)
        )
        self.data.loc[collapse_mask, 'greek_regime'] = 'collapse'
        
        regime_analysis = {
            'volatility_regimes': self.data['volatility_regime'].value_counts().to_dict(),
            'greek_regimes': self.data['greek_regime'].value_counts().to_dict(),
            'regime_data': self.data[['date', 'volatility_regime', 'greek_regime', 'spx_close']].copy()
        }
        
        self.analysis_results['regimes'] = regime_analysis
        
        print(f"Market Regime Analysis:")
        print(f"  Volatility regimes: {regime_analysis['volatility_regimes']}")
        print(f"  Greek regimes: {regime_analysis['greek_regimes']}")
        
        return regime_analysis
    
    def run_full_analysis(self) -> Dict:
        """Run complete analysis pipeline"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        print("Running full Greeks analysis...")
        
        # Run all analysis components
        self.identify_convergence_events()
        self.analyze_vomma_price_divergence()
        self.analyze_theta_decay_urgency()
        self.analyze_volume_patterns()
        self.calculate_greek_correlations()
        self.identify_market_regimes()
        
        # Generate summary
        summary = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_period': {
                'start': self.data['date'].min().strftime('%Y-%m-%d'),
                'end': self.data['date'].max().strftime('%Y-%m-%d'),
                'total_days': len(self.data)
            },
            'key_findings': {
                'total_convergence_events': len(self.analysis_results['convergence_events']),
                'distribution_signals': len(self.analysis_results['vomma_divergence']['distribution_signals']),
                'volume_collapse_events': len(self.analysis_results['volume_analysis']['volume_collapse_events']),
                'extreme_greek_days': len(self.data[self.data['extreme_score'] >= 4])
            }
        }
        
        self.analysis_results['summary'] = summary
        
        print("Analysis complete!")
        print(f"Key findings:")
        for key, value in summary['key_findings'].items():
            print(f"  {key}: {value}")
        
        return self.analysis_results
    
    def get_analysis_results(self) -> Dict:
        """Get all analysis results including processed data with extreme scores"""
        # Ensure the main data includes extreme_score
        if hasattr(self, 'data') and 'extreme_score' in self.data.columns:
            self.analysis_results['processed_data'] = self.data.copy()
        
        return self.analysis_results

