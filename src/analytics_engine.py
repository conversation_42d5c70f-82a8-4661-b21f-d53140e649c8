"""
Analytics Engine for SPX Options Greeks Analysis
Implements pattern recognition and extreme event analysis
"""
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Import the new convergence processor
try:
    from convergence_processor import ConvergenceAnalyzer
    CONVERGENCE_PROCESSOR_AVAILABLE = True
except ImportError as e:
    CONVERGENCE_PROCESSOR_AVAILABLE = False
    print(f"Warning: convergence_processor not available ({e}), using fallback convergence analysis")

class GreeksAnalyticsEngine:
    """Analytics engine for Greeks pattern recognition and analysis"""
    
    def __init__(self, config):
        self.config = config
        self.data = None
        self.raw_data = None
        self.data_loader = None
        self.convergence_analyzer = None
        self.analysis_results = {}

    def set_data(self, daily_aggregates: pd.DataFrame):
        """Set the daily aggregated data for analysis"""
        self.data = daily_aggregates.copy()
        self.data['date'] = pd.to_datetime(self.data['date'])
        self.data = self.data.sort_values('date').reset_index(drop=True)

    def set_raw_data(self, raw_processed_data: pd.DataFrame):
        """Set the raw processed data for forward-looking analysis"""
        self.raw_data = raw_processed_data.copy()
        self.raw_data['date'] = pd.to_datetime(self.raw_data['date'])
        self.raw_data['Expiry Date'] = pd.to_datetime(self.raw_data['Expiry Date'])
        self.raw_data = self.raw_data.sort_values(['date', 'Expiry Date']).reset_index(drop=True)

    def set_data_loader(self, data_loader):
        """Set the data loader for convergence analysis"""
        self.data_loader = data_loader

        # Initialize convergence analyzer if available
        if CONVERGENCE_PROCESSOR_AVAILABLE and data_loader is not None:
            self.convergence_analyzer = ConvergenceAnalyzer(data_loader)
            print("✓ Convergence processor initialized")
        else:
            print(f"Debug: CONVERGENCE_PROCESSOR_AVAILABLE={CONVERGENCE_PROCESSOR_AVAILABLE}, data_loader={data_loader is not None}")
        
    def identify_convergence_events(self) -> pd.DataFrame:
        """
        Identify convergence events using the new convergence processor if available,
        otherwise fall back to the original forward-looking analysis.

        Returns:
            pd.DataFrame: DataFrame containing convergence events
        """
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")

        # Use the new convergence processor if available
        if CONVERGENCE_PROCESSOR_AVAILABLE and self.convergence_analyzer is not None:
            print("✓ Using convergence processor for analysis")
            return self._identify_convergence_with_processor()

        # Fall back to original forward-looking analysis
        elif self.raw_data is not None and len(self.raw_data) > 0:
            print(f"Debug: Using fallback forward analysis - processor_available={CONVERGENCE_PROCESSOR_AVAILABLE}, analyzer={self.convergence_analyzer is not None}")
            return self._identify_forward_convergence_events()
        else:
            print("Warning: No raw data available for forward-looking analysis, using historical aggregates")
            return self._identify_historical_convergence_events()

    def _identify_forward_convergence_events(self) -> pd.DataFrame:
        """Identify convergence events using forward-looking strike-specific data"""
        # Focus on future expiry dates only
        today = pd.Timestamp.now().date()
        future_data = self.raw_data[
            (self.raw_data['Expiry Date'].dt.date > today) &
            (self.raw_data['DTE'] >= 1)  # At least 1 day to expiration
        ].copy()

        if len(future_data) == 0:
            print("No future expiry data available for convergence analysis")
            return pd.DataFrame()

        print(f"Analyzing forward convergence using {len(future_data)} future option strikes")

        # Group by expiry date to identify convergence patterns
        agg_dict = {
            'Vomma': ['sum', 'std', 'count'],
            'Vanna': ['sum', 'std', 'count'],
            'Charm': ['sum', 'std', 'count'],
            'Theta': ['sum', 'std', 'count'],
            'Open Interest': ['sum', 'mean'],
            'Strike': ['min', 'max', 'count'],
            'DTE': 'first',
            'date': 'first'
        }

        # Add dynamic close price column
        ticker = self.config.TICKER.lower()
        close_col = f'{ticker}_close'
        if close_col in future_data.columns:
            agg_dict[close_col] = 'first'

        expiry_groups = future_data.groupby('Expiry Date').agg(agg_dict).reset_index()

        # Flatten column names dynamically
        new_columns = ['Expiry Date']
        for col in expiry_groups.columns[1:]:  # Skip 'Expiry Date'
            if isinstance(col, tuple):
                if col[0] == close_col:
                    new_columns.append(close_col)
                elif col[0] == 'Vomma':
                    new_columns.append(f'Vomma_{col[1].title()}')
                elif col[0] == 'Vanna':
                    new_columns.append(f'Vanna_{col[1].title()}')
                elif col[0] == 'Charm':
                    new_columns.append(f'Charm_{col[1].title()}')
                elif col[0] == 'Theta':
                    new_columns.append(f'Theta_{col[1].title()}')
                elif col[0] == 'Open Interest':
                    new_columns.append(f'OI_{col[1].title()}')
                elif col[0] == 'Strike':
                    new_columns.append(f'Strike_{col[1].title()}')
                else:
                    new_columns.append(col[0])
            else:
                new_columns.append(col)

        expiry_groups.columns = new_columns

        # Calculate forward-looking thresholds based on expiry-grouped data
        config = self.config.DATA_DRIVEN_CONFIG
        extreme_pct = config['extreme_percentile']

        # Calculate base thresholds using percentiles
        base_vomma_threshold = abs(expiry_groups['Vomma_Sum']).quantile(extreme_pct)
        base_vanna_threshold = abs(expiry_groups['Vanna_Sum']).quantile(extreme_pct)
        base_charm_threshold = abs(expiry_groups['Charm_Sum']).quantile(extreme_pct)
        base_theta_threshold = abs(expiry_groups['Theta_Sum']).quantile(extreme_pct)
        base_oi_threshold = expiry_groups['OI_Sum'].quantile(extreme_pct)

        # Apply convergence multipliers for more stringent thresholds
        vomma_threshold = base_vomma_threshold * config['convergence_vomma_multiplier']
        vanna_threshold = base_vanna_threshold * config['convergence_vanna_multiplier']
        charm_threshold = base_charm_threshold * config['convergence_charm_multiplier']
        theta_threshold = base_theta_threshold * config['convergence_theta_multiplier']
        oi_threshold = base_oi_threshold * config['convergence_oi_multiplier']

        print(f"Forward-looking convergence thresholds (by expiry date):")
        print(f"  Vomma: {vomma_threshold:.1f}")
        print(f"  Vanna: {vanna_threshold:.1f}")
        print(f"  Charm: {charm_threshold:.1f}")
        print(f"  Theta: {theta_threshold:.1f}")
        print(f"  Open Interest: {oi_threshold:.1f}")

        # Create forward-looking scoring system
        expiry_groups['extreme_score'] = 0

        # Score based on extreme Greek concentrations at specific expiry dates
        expiry_groups.loc[abs(expiry_groups['Vomma_Sum']) > vomma_threshold, 'extreme_score'] += config['vomma_score_weight']
        expiry_groups.loc[abs(expiry_groups['Vanna_Sum']) > vanna_threshold, 'extreme_score'] += config['vanna_score_weight']
        expiry_groups.loc[abs(expiry_groups['Charm_Sum']) > charm_threshold, 'extreme_score'] += config['charm_score_weight']
        expiry_groups.loc[abs(expiry_groups['Theta_Sum']) > theta_threshold, 'extreme_score'] += config['theta_score_weight']
        expiry_groups.loc[expiry_groups['OI_Sum'] > oi_threshold, 'extreme_score'] += config['oi_score_weight']

        # Add bonus points for high concentration (many strikes at same expiry)
        strike_concentration_threshold = expiry_groups['Strike_Count'].quantile(0.8)  # Top 20% by strike count
        expiry_groups.loc[expiry_groups['Strike_Count'] > strike_concentration_threshold, 'extreme_score'] += 1

        # Identify forward convergence events
        min_score = config['convergence_min_score']
        forward_convergence = expiry_groups[expiry_groups['extreme_score'] >= min_score].copy()

        # Add event classification
        forward_convergence['event_type'] = 'forward_convergence'
        forward_convergence.loc[forward_convergence['extreme_score'] >= config['major_convergence_min_score'], 'event_type'] = 'major_forward_convergence'
        forward_convergence.loc[forward_convergence['extreme_score'] >= config['historic_convergence_min_score'], 'event_type'] = 'historic_forward_convergence'

        # Store results
        self.analysis_results['forward_convergence_events'] = forward_convergence
        self.analysis_results['convergence_events'] = forward_convergence  # Use forward events as primary

        print(f"Identified {len(forward_convergence)} forward convergence events")
        print(f"  Major forward convergences: {len(forward_convergence[forward_convergence['event_type'] == 'major_forward_convergence'])}")
        print(f"  Historic forward convergences: {len(forward_convergence[forward_convergence['event_type'] == 'historic_forward_convergence'])}")

        return forward_convergence

    def _identify_convergence_with_processor(self) -> pd.DataFrame:
        """Use the new convergence processor to identify convergence events"""
        try:
            # Get future expiry dates for analysis
            today = pd.Timestamp.now().date()
            future_data = self.raw_data[
                (self.raw_data['Expiry Date'].dt.date > today) &
                (self.raw_data['DTE'] >= 1)
            ].copy()

            if len(future_data) == 0:
                print("No future expiry data available for convergence processor")
                return pd.DataFrame()

            # Get unique future expiry dates
            future_expiry_dates = future_data['Expiry Date'].dt.strftime('%Y-%m-%d').unique()
            print(f"Analyzing convergence using processor for {len(future_expiry_dates)} future expiry dates")

            # Analyze convergence for these dates
            convergence_data = self.convergence_analyzer.analyze_convergence_dates(future_expiry_dates.tolist())

            if len(convergence_data) == 0:
                print("No convergence data generated by processor")
                return pd.DataFrame()

            # Calculate convergence signals
            convergence_signals = self.convergence_analyzer.calculate_convergence_signals()

            # Filter for significant convergence events
            significant_events = convergence_signals[
                (convergence_signals['signal_strength'] != 0) |
                (convergence_signals['bearish_signal'] > 0) |
                (convergence_signals['bullish_signal'] > 0) |
                (convergence_signals['explosive_signal'] > 0)
            ].copy()

            if len(significant_events) > 0:
                # Convert date column to Expiry Date for compatibility
                significant_events['Expiry Date'] = pd.to_datetime(significant_events['date'])

                # Add event type classification based on signal strength
                significant_events['event_type'] = 'convergence'
                significant_events.loc[significant_events['explosive_signal'] > 0, 'event_type'] = 'explosive_convergence'
                significant_events.loc[significant_events['bullish_signal'] > 0, 'event_type'] = 'bullish_convergence'
                significant_events.loc[significant_events['bearish_signal'] > 0, 'event_type'] = 'bearish_convergence'

                # Store results
                self.analysis_results['convergence_processor_data'] = convergence_signals
                self.analysis_results['convergence_events'] = significant_events

                print(f"Convergence processor identified {len(significant_events)} significant convergence events:")
                for event_type in significant_events['event_type'].value_counts().items():
                    print(f"  {event_type[0]}: {event_type[1]} events")

                return significant_events
            else:
                print("No significant convergence events found by processor")
                return pd.DataFrame()

        except Exception as e:
            print(f"Error in convergence processor: {e}")
            print("Falling back to original forward convergence analysis")
            return self._identify_forward_convergence_events()

    def _identify_historical_convergence_events(self) -> pd.DataFrame:
        """Fallback method for historical convergence analysis when raw data is unavailable"""
        # This is the original method logic for backward compatibility
        config = self.config.DATA_DRIVEN_CONFIG
        extreme_pct = config['extreme_percentile']

        vomma_threshold = abs(self.data['Vomma_M']).quantile(extreme_pct)
        vanna_threshold = abs(self.data['Vanna_K']).quantile(extreme_pct)
        charm_threshold = abs(self.data['Charm_M']).quantile(extreme_pct)
        theta_threshold = abs(self.data['Theta_M']).quantile(extreme_pct)
        oi_threshold = self.data['Total_OI_K'].quantile(extreme_pct)

        delta_pc_low = self.data['Delta_PC_Ratio'].quantile(1 - extreme_pct)
        delta_pc_high = self.data['Delta_PC_Ratio'].quantile(extreme_pct)

        print(f"Historical convergence thresholds:")
        print(f"  Vomma: {vomma_threshold:.1f}M")
        print(f"  Vanna: {vanna_threshold:.1f}K")
        print(f"  Charm: {charm_threshold:.1f}M")
        print(f"  Theta: {theta_threshold:.1f}M")
        print(f"  Open Interest: {oi_threshold:.1f}K")
        print(f"  Delta P/C Ratio: {delta_pc_low:.3f} (bullish) / {delta_pc_high:.3f} (bearish)")

        self.data['extreme_score'] = 0
        self.data.loc[abs(self.data['Vomma_M']) > vomma_threshold, 'extreme_score'] += config['vomma_score_weight']
        self.data.loc[abs(self.data['Vanna_K']) > vanna_threshold, 'extreme_score'] += config['vanna_score_weight']
        self.data.loc[abs(self.data['Charm_M']) > charm_threshold, 'extreme_score'] += config['charm_score_weight']
        self.data.loc[abs(self.data['Theta_M']) > theta_threshold, 'extreme_score'] += config['theta_score_weight']
        self.data.loc[self.data['Total_OI_K'] > oi_threshold, 'extreme_score'] += config['oi_score_weight']
        self.data.loc[self.data['Delta_PC_Ratio'] < delta_pc_low, 'extreme_score'] += config['delta_pc_score_weight']
        self.data.loc[self.data['Delta_PC_Ratio'] > delta_pc_high, 'extreme_score'] += config['delta_pc_score_weight']

        min_score = config['convergence_min_score']
        convergence_events = self.data[self.data['extreme_score'] >= min_score].copy()
        convergence_events['event_type'] = 'historical_convergence'

        self.analysis_results['convergence_events'] = convergence_events

        print(f"Identified {len(convergence_events)} historical convergence events")
        return convergence_events
    
    def analyze_vomma_price_divergence(self) -> Dict:
        """
        Analyze Vomma/Price divergence patterns as described in documents.

        Divergence occurs when price movements don't align with Vomma movements,
        potentially indicating:
        - Market inefficiencies
        - Upcoming volatility changes
        - Options positioning imbalances

        Patterns analyzed:
        - Price up + Vomma down (bearish divergence)
        - Price down + Vomma up (bullish divergence)
        - Large Vomma changes without corresponding price moves

        Returns:
            Dict: Dictionary containing divergence statistics and patterns
        """
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")

        # Calculate price changes using configuration constants and dynamic column names
        config = self.config.DATA_DRIVEN_CONFIG
        percentage_multiplier = config['percentage_multiplier']

        ticker = self.config.TICKER.lower()
        close_col = f'{ticker}_close'

        self.data['price_change'] = self.data[close_col].diff()
        self.data['price_change_pct'] = self.data[close_col].pct_change() * percentage_multiplier

        # Calculate Vomma changes
        self.data['vomma_change'] = self.data['Vomma_M'].diff()
        self.data['vomma_change_pct'] = self.data['Vomma_M'].pct_change() * percentage_multiplier

        # Use data-driven thresholds for divergence patterns
        medium_high_pct = config['medium_high_percentile']  # 75th percentile
        low_pct = config['low_percentile']                  # 25th percentile

        vomma_high_threshold = self.data['Vomma_M'].quantile(medium_high_pct)  # 75th percentile for high Vomma
        vomma_low_threshold = self.data['Vomma_M'].quantile(low_pct)           # 25th percentile for low Vomma
        vomma_change_threshold = abs(self.data['vomma_change']).quantile(medium_high_pct)  # 75th percentile for large changes

        # Identify divergence patterns
        # Pattern 1: Price rises while Vomma explodes (distribution signal)
        distribution_pattern = (
            (self.data['price_change'] > 0) &  # Price rising
            (self.data['Vomma_M'] > vomma_high_threshold) &  # High Vomma (data-driven)
            (self.data['vomma_change'] > vomma_change_threshold)  # Vomma increasing significantly
        )

        # Pattern 2: Negative Vomma with price decline (acceleration pattern)
        acceleration_pattern = (
            (self.data['price_change'] < 0) &  # Price falling
            (self.data['Vomma_M'] < vomma_low_threshold) &  # Low/Negative Vomma (data-driven)
            (abs(self.data['vomma_change']) > vomma_change_threshold)  # Large Vomma change
        )
        
        divergence_analysis = {
            'distribution_signals': self.data[distribution_pattern].copy(),
            'acceleration_signals': self.data[acceleration_pattern].copy(),
            'correlation_vomma_price': self.data['Vomma_M'].corr(self.data[close_col]),
            'correlation_vomma_price_change': self.data['Vomma_M'].corr(self.data['price_change'])
        }
        
        self.analysis_results['vomma_divergence'] = divergence_analysis
        
        print(f"Vomma/Price Divergence Analysis:")
        print(f"  Distribution signals: {len(divergence_analysis['distribution_signals'])}")
        print(f"  Acceleration signals: {len(divergence_analysis['acceleration_signals'])}")
        print(f"  Vomma-Price correlation: {divergence_analysis['correlation_vomma_price']:.3f}")
        
        return divergence_analysis
    
    def analyze_theta_decay_urgency(self) -> Dict:
        """Analyze Theta decay patterns and urgency signals"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Calculate Theta changes and patterns using configuration constants
        config = self.config.DATA_DRIVEN_CONFIG
        percentage_multiplier = config['percentage_multiplier']

        # Get dynamic close column name
        ticker = self.config.TICKER.lower()
        close_col = f'{ticker}_close'

        self.data['theta_change'] = self.data['Theta_M'].diff()
        self.data['theta_change_pct'] = self.data['Theta_M'].pct_change() * percentage_multiplier

        # Use configurable data-driven thresholds for theta analysis
        extreme_pct = config['extreme_percentile']  # 90th percentile
        low_pct = config['low_percentile']          # 25th percentile

        theta_change_threshold = self.data['theta_change'].quantile(extreme_pct)  # Large theta changes
        theta_low_threshold = abs(self.data['Theta_M']).quantile(low_pct)         # Low theta values
        theta_high_threshold = abs(self.data['Theta_M']).quantile(extreme_pct)    # High theta values
        vomma_high_threshold = abs(self.data['Vomma_M']).quantile(extreme_pct)    # High vomma values

        # Identify urgency patterns
        # Pattern 1: Massive Theta collapse (urgency to exit)
        theta_collapse = (
            (self.data['theta_change'] > theta_change_threshold) &  # Large positive change (less negative)
            (abs(self.data['Theta_M']) < theta_low_threshold)  # Low absolute theta (near zero)
        )

        # Pattern 3: Vomma collapse (volatility risk collapse)
        vomma_low_threshold = abs(self.data['Vomma_M']).quantile(0.25)  # Bottom 25th percentile
        vomma_collapse = (
            (abs(self.data['Vomma_M']) < vomma_low_threshold) &  # Low Vomma values
            (self.data[close_col] < self.data[close_col].rolling(5).mean())  # Price declining
        )

        # Pattern 2: High Theta with high Vomma (dangerous combination)
        dangerous_combo = (
            (abs(self.data['Theta_M']) > theta_high_threshold) &  # High Theta (data-driven)
            (abs(self.data['Vomma_M']) > vomma_high_threshold)  # High Vomma (data-driven)
        )
        
        theta_analysis = {
            'theta_collapse_events': self.data[theta_collapse].copy(),
            'dangerous_combinations': self.data[dangerous_combo].copy(),
            'theta_statistics': {
                'mean': self.data['Theta_M'].mean(),
                'std': self.data['Theta_M'].std(),
                'min': self.data['Theta_M'].min(),
                'max': self.data['Theta_M'].max()
            }
        }
        
        self.analysis_results['theta_analysis'] = theta_analysis
        
        print(f"Theta Decay Analysis:")
        print(f"  Theta collapse events: {len(theta_analysis['theta_collapse_events'])}")
        print(f"  Dangerous combinations: {len(theta_analysis['dangerous_combinations'])}")
        
        return theta_analysis
    
    def analyze_volume_patterns(self) -> Dict:
        """Analyze Open Interest collapse patterns (institutional position unwinding)"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")

        # Calculate Open Interest changes using configuration constants
        config = self.config.DATA_DRIVEN_CONFIG
        percentage_multiplier = config['percentage_multiplier']

        self.data['oi_change'] = self.data['Total_OI_K'].diff()
        self.data['oi_change_pct'] = self.data['Total_OI_K'].pct_change() * percentage_multiplier

        # Calculate rolling OI averages using configuration constants
        short_period = config['volume_ma_short_period']  # 5 days
        long_period = config['volume_ma_long_period']    # 10 days

        self.data['oi_ma_5'] = self.data['Total_OI_K'].rolling(short_period).mean()
        self.data['oi_ma_10'] = self.data['Total_OI_K'].rolling(long_period).mean()

        # Historical OI collapse detection (daily data)
        collapse_threshold = config['volume_collapse_threshold']  # -50%
        oi_collapse = (
            (self.data['oi_change_pct'] < collapse_threshold) &  # >50% OI drop
            (self.data['Total_OI_K'] > 0)  # Valid OI data
        )

        # Historical OI spikes (institutional buildup)
        spike_threshold = config['volume_spike_threshold']    # 100%
        volume_multiplier = config['volume_ma_multiplier']    # 2x
        oi_spike = (
            (self.data['oi_change_pct'] > spike_threshold) &  # >100% OI increase
            (self.data['Total_OI_K'] > self.data['oi_ma_10'] * volume_multiplier)  # Above 2x average
        )

        # FORWARD-LOOKING OI COLLAPSE DETECTION (July events)
        forward_oi_collapse = []
        forward_greeks_collapse = []

        if self.raw_data is not None and len(self.raw_data) > 0:
            # Analyze future expiry dates for collapse patterns
            today = pd.Timestamp.now().normalize()
            future_data = self.raw_data[pd.to_datetime(self.raw_data['Expiry Date']) > today].copy()

            if len(future_data) > 0:
                # Group by expiry date to find collapse patterns
                expiry_groups = future_data.groupby('Expiry Date').agg({
                    'Open Interest': ['sum', 'mean', 'count'],
                    'Vomma': ['sum', 'mean'],
                    'Vanna': ['sum', 'mean'],
                    'Charm': ['sum', 'mean']
                }).round(2)

                expiry_groups.columns = ['OI_Sum', 'OI_Mean', 'Strike_Count', 'Vomma_Sum', 'Vomma_Mean', 'Vanna_Sum', 'Vanna_Mean', 'Charm_Sum', 'Charm_Mean']
                expiry_groups = expiry_groups.reset_index()

                # Detect OI collapse: very low OI concentration at specific expiry dates
                oi_collapse_threshold = expiry_groups['OI_Sum'].quantile(0.15)  # Bottom 15%
                july_oi_collapse = expiry_groups[
                    (expiry_groups['Expiry Date'].dt.month == 7) &
                    (expiry_groups['OI_Sum'] < oi_collapse_threshold)
                ]

                # Detect Greeks collapse: very low Greeks values at specific expiry dates
                vomma_collapse_threshold = expiry_groups['Vomma_Sum'].quantile(0.15)  # Bottom 15%
                vanna_collapse_threshold = expiry_groups['Vanna_Sum'].quantile(0.15)  # Bottom 15%
                july_greeks_collapse = expiry_groups[
                    (expiry_groups['Expiry Date'].dt.month == 7) &
                    ((expiry_groups['Vomma_Sum'] < vomma_collapse_threshold) |
                     (expiry_groups['Vanna_Sum'] < vanna_collapse_threshold))
                ]

                forward_oi_collapse = july_oi_collapse.to_dict('records')
                forward_greeks_collapse = july_greeks_collapse.to_dict('records')

        volume_analysis = {
            'volume_collapse_events': self.data[oi_collapse].copy(),
            'volume_spike_events': self.data[oi_spike].copy(),
            'forward_oi_collapse_events': forward_oi_collapse,
            'forward_greeks_collapse_events': forward_greeks_collapse,
            'volume_statistics': {
                'mean': self.data['Total_OI_K'].mean(),
                'median': self.data['Total_OI_K'].median(),
                'std': self.data['Total_OI_K'].std()
            }
        }

        self.analysis_results['volume_analysis'] = volume_analysis

        print(f"Open Interest Pattern Analysis:")
        print(f"  Historical OI collapse events: {len(volume_analysis['volume_collapse_events'])}")
        print(f"  Historical OI spike events: {len(volume_analysis['volume_spike_events'])}")
        print(f"  Forward OI collapse events (July): {len(forward_oi_collapse)}")
        print(f"  Forward Greeks collapse events (July): {len(forward_greeks_collapse)}")

        return volume_analysis
    
    def calculate_greek_correlations(self) -> Dict:
        """Calculate correlations between different Greeks and price"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")

        # Check if we have sufficient data for correlation analysis
        if len(self.data) < 2:
            print(f"Warning: Only {len(self.data)} data point(s) available - correlations will be NaN")

        # Select Greek columns for correlation analysis using dynamic column names
        greek_columns = ['Vomma_M', 'Vanna_K', 'Charm_M', 'Theta_M', 'Delta_M', 'Gamma_M', 'Vega_M']
        ticker = self.config.TICKER.lower()
        price_columns = [f'{ticker}_close', f'{ticker}_high', f'{ticker}_low']

        # Calculate correlation matrix
        correlation_data = self.data[greek_columns + price_columns + ['Total_OI_K', 'Volume', 'Delta_PC_Ratio']]
        correlation_matrix = correlation_data.corr()

        # Extract key correlations using dynamic column names with NaN handling
        close_col = f'{ticker}_close'

        def safe_correlation(matrix, row, col):
            """Safely extract correlation value, return 0.0 if NaN"""
            try:
                value = matrix.loc[row, col]
                return 0.0 if pd.isna(value) else value
            except (KeyError, IndexError):
                return 0.0

        key_correlations = {
            'vomma_price': safe_correlation(correlation_matrix, 'Vomma_M', close_col),
            'vanna_price': safe_correlation(correlation_matrix, 'Vanna_K', close_col),
            'charm_price': safe_correlation(correlation_matrix, 'Charm_M', close_col),
            'theta_price': safe_correlation(correlation_matrix, 'Theta_M', close_col),
            'oi_price': safe_correlation(correlation_matrix, 'Total_OI_K', close_col),
            'volume_price': safe_correlation(correlation_matrix, 'Volume', close_col),
            'vomma_vanna': safe_correlation(correlation_matrix, 'Vomma_M', 'Vanna_K'),
            'vomma_oi': safe_correlation(correlation_matrix, 'Vomma_M', 'Total_OI_K'),
        }

        correlations_analysis = {
            'correlation_matrix': correlation_matrix,
            'key_correlations': key_correlations,
            'greek_columns': greek_columns,
            'price_columns': price_columns
        }

        self.analysis_results['correlations'] = correlations_analysis

        print(f"Greek Correlations Analysis:")
        if len(self.data) >= 2:
            print(f"  Vomma-Price correlation: {key_correlations['vomma_price']:.3f}")
            print(f"  Vanna-Price correlation: {key_correlations['vanna_price']:.3f}")
            print(f"  Vomma-Vanna correlation: {key_correlations['vomma_vanna']:.3f}")
        else:
            print(f"  Correlations: Insufficient data ({len(self.data)} points) - using fallback values")

        return correlations_analysis
    
    def identify_market_regimes(self) -> Dict:
        """Identify different market regimes based on Greeks patterns"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        # Calculate regime indicators
        self.data['volatility_regime'] = 'normal'
        self.data['greek_regime'] = 'normal'
        
        # Use configuration constants for regime thresholds
        config = self.config.DATA_DRIVEN_CONFIG
        vol_high_threshold = config['vol_regime_high_threshold']      # 0.80
        vol_low_threshold = config['vol_regime_low_threshold']        # 0.20
        extreme_threshold = config['greek_regime_extreme_threshold']  # 0.85
        distribution_threshold = config['greek_regime_distribution_threshold']  # 0.75
        collapse_threshold = config['greek_regime_collapse_threshold']  # 0.25

        # High volatility regime
        high_vol_threshold = self.data['Vega_M'].quantile(vol_high_threshold)
        self.data.loc[self.data['Vega_M'] > high_vol_threshold, 'volatility_regime'] = 'high_vol'

        # Low volatility regime
        low_vol_threshold = self.data['Vega_M'].quantile(vol_low_threshold)
        self.data.loc[self.data['Vega_M'] < low_vol_threshold, 'volatility_regime'] = 'low_vol'

        # Use data-driven thresholds for regime classification
        vomma_extreme_threshold = abs(self.data['Vomma_M']).quantile(extreme_threshold)
        vanna_extreme_threshold = abs(self.data['Vanna_K']).quantile(extreme_threshold)
        oi_extreme_threshold = self.data['Total_OI_K'].quantile(extreme_threshold)
        vomma_distribution_threshold = self.data['Vomma_M'].quantile(distribution_threshold)
        vanna_distribution_threshold = self.data['Vanna_K'].quantile(distribution_threshold)
        vomma_collapse_threshold = self.data['Vomma_M'].quantile(collapse_threshold)

        # Extreme Greeks regime (using configuration threshold)
        extreme_greek_mask = (
            (abs(self.data['Vomma_M']) > vomma_extreme_threshold) |
            (abs(self.data['Vanna_K']) > vanna_extreme_threshold) |
            (self.data['Total_OI_K'] > oi_extreme_threshold)
        )
        self.data.loc[extreme_greek_mask, 'greek_regime'] = 'extreme'

        # Distribution regime (price up, high Greeks - using configuration threshold)
        distribution_mask = (
            (self.data['price_change'] > 0) &
            (self.data['Vomma_M'] > vomma_distribution_threshold) &
            (self.data['Vanna_K'] > vanna_distribution_threshold)
        )
        self.data.loc[distribution_mask, 'greek_regime'] = 'distribution'

        # Collapse regime (low Vomma, price down - using configuration threshold)
        collapse_mask = (
            (self.data['Vomma_M'] < vomma_collapse_threshold) &
            (self.data['price_change'] < 0)
        )
        self.data.loc[collapse_mask, 'greek_regime'] = 'collapse'
        
        regime_analysis = {
            'volatility_regimes': self.data['volatility_regime'].value_counts().to_dict(),
            'greek_regimes': self.data['greek_regime'].value_counts().to_dict(),
            'regime_data': self.data[['date', 'volatility_regime', 'greek_regime', f'{self.config.TICKER.lower()}_close']].copy()
        }
        
        self.analysis_results['regimes'] = regime_analysis
        
        print(f"Market Regime Analysis:")
        print(f"  Volatility regimes: {regime_analysis['volatility_regimes']}")
        print(f"  Greek regimes: {regime_analysis['greek_regimes']}")
        
        return regime_analysis
    
    def run_full_analysis(self) -> Dict:
        """Run complete analysis pipeline"""
        if self.data is None:
            raise ValueError("No data set. Call set_data() first.")
        
        print("Running full Greeks analysis...")
        
        # Run all analysis components
        self.identify_convergence_events()
        self.analyze_vomma_price_divergence()
        self.analyze_theta_decay_urgency()
        self.analyze_volume_patterns()
        self.calculate_greek_correlations()
        self.identify_market_regimes()
        
        # Generate summary
        summary = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_period': {
                'start': self.data['date'].min().strftime('%Y-%m-%d'),
                'end': self.data['date'].max().strftime('%Y-%m-%d'),
                'total_days': len(self.data)
            },
            'key_findings': {
                'total_convergence_events': len(self.analysis_results['convergence_events']),
                'distribution_signals': len(self.analysis_results['vomma_divergence']['distribution_signals']),
                'volume_collapse_events': len(self.analysis_results['volume_analysis']['volume_collapse_events']),
                'extreme_greek_days': len(self.data[self.data['extreme_score'] >= 4]) if 'extreme_score' in self.data.columns else 0
            }
        }
        
        self.analysis_results['summary'] = summary
        
        print("Analysis complete!")
        print(f"Key findings:")
        for key, value in summary['key_findings'].items():
            print(f"  {key}: {value}")
        
        return self.analysis_results
    
    def get_analysis_results(self) -> Dict:
        """Get all analysis results including processed data with extreme scores"""
        # Ensure the main data includes extreme_score
        if hasattr(self, 'data') and 'extreme_score' in self.data.columns:
            self.analysis_results['processed_data'] = self.data.copy()

        return self.analysis_results

    def add_raw_data_sample(self, data_loader):
        """Add raw data sample for expiry analysis charts"""
        if hasattr(data_loader, 'get_raw_data_sample'):
            self.analysis_results['raw_data_sample'] = data_loader.get_raw_data_sample()
        return self.analysis_results

