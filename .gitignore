# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Data files (sensitive)
*.csv
*.xlsx
*.xls
optionhistory/
data/

# Environment variables (contains API keys)
.env
.env.local
.env.production
.env.staging

# Reports and outputs (optional - remove if you want to track them)
reports/*.png
reports/*.md
reports/*.pdf

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/

# Jupyter Notebooks
.ipynb_checkpoints/
*.ipynb

# pytest
.pytest_cache/
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
