../../../bin/weasyprint,sha256=oTIpcYLBororVFVqv2Q2OBuN9Vy-SB2_WxEiFfQHVZ8,270
weasyprint-65.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
weasyprint-65.1.dist-info/METADATA,sha256=v9-RDucK9Kk_K1t1UO7i1KRMr7GRF-bbdCdt9XGn-qg,3707
weasyprint-65.1.dist-info/RECORD,,
weasyprint-65.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
weasyprint-65.1.dist-info/WHEEL,sha256=G2gURzTEtmeR8nrdXUJfNiB3VYVxigPQ-bEQujpNiNs,82
weasyprint-65.1.dist-info/entry_points.txt,sha256=wgDp3XXzFywdYgI5vUWMp1zAwx1sZXXH0FTUQbFOq6A,55
weasyprint-65.1.dist-info/licenses/LICENSE,sha256=v9FOzPphAFdUYOaFVWsYM5nUvTNZBOPJUhsBFtIcVNo,1534
weasyprint/__init__.py,sha256=MrV26630NkuBXozSOqGdsFZWOFI6arULa1nSj-7EYM8,17409
weasyprint/__main__.py,sha256=WaRdNsuuTBCJlny8AgqF9I_0Mnx5GseQEFA5lXimErw,7114
weasyprint/__pycache__/__init__.cpython-312.pyc,,
weasyprint/__pycache__/__main__.cpython-312.pyc,,
weasyprint/__pycache__/anchors.cpython-312.pyc,,
weasyprint/__pycache__/document.cpython-312.pyc,,
weasyprint/__pycache__/html.cpython-312.pyc,,
weasyprint/__pycache__/images.cpython-312.pyc,,
weasyprint/__pycache__/logger.cpython-312.pyc,,
weasyprint/__pycache__/matrix.cpython-312.pyc,,
weasyprint/__pycache__/stacking.cpython-312.pyc,,
weasyprint/__pycache__/urls.cpython-312.pyc,,
weasyprint/anchors.py,sha256=yXEZD0uFsCAdDjC07cHgWqRuGOSsOsmmmk4omZZecAo,6428
weasyprint/css/__init__.py,sha256=6w2UoHQSoZ76eCuferHS3R0M8nAzFgab1NwR1vpy9LA,51599
weasyprint/css/__pycache__/__init__.cpython-312.pyc,,
weasyprint/css/__pycache__/computed_values.cpython-312.pyc,,
weasyprint/css/__pycache__/counters.cpython-312.pyc,,
weasyprint/css/__pycache__/media_queries.cpython-312.pyc,,
weasyprint/css/__pycache__/properties.cpython-312.pyc,,
weasyprint/css/__pycache__/targets.cpython-312.pyc,,
weasyprint/css/__pycache__/utils.cpython-312.pyc,,
weasyprint/css/computed_values.py,sha256=O3USpx2ZH7Ff_FN5oIjn40lNMHj3zdThR0r51HRniIQ,27301
weasyprint/css/counters.py,sha256=DHSrGJr2ktpZLCc-JYIiE67ak0TORsKSBKkjju-qwdE,11373
weasyprint/css/html5_ph.css,sha256=l8t4ZN3KoevKx0UEfNw3_vgVjArcII6y5DZXZolWaw0,4629
weasyprint/css/html5_ua.css,sha256=qGUxl1eIu3tQRVu2fVMnC_oo_CbMYoukAC8dSvH1ErM,19111
weasyprint/css/html5_ua_form.css,sha256=0JLdrpmhokuy9IFhK2L1ZwbTa2G3ibXTXcGuCkAw80Y,288
weasyprint/css/media_queries.py,sha256=wHPteZ9Gs2ttuA5kZpMDgRtFHnhYZVwrFXrhKgmR-4g,1072
weasyprint/css/properties.py,sha256=5nywzyQjLOSKKGxsvJsVgoyQfMsLSQarMeso-WRgYlU,11593
weasyprint/css/targets.py,sha256=5Ofw1RrmPsfQjDuZ1FCgktspGUai3wJmNa03MbT2sOI,8853
weasyprint/css/utils.py,sha256=bRZYaMDRGtlHqyQouzZRAA-SfMep6_xGe-nnIbUUG_Y,25105
weasyprint/css/validation/__init__.py,sha256=tuyJXGP6TbUdVi_KtxLj0nFa49vGF5KNZkXI8HxR_cw,8468
weasyprint/css/validation/__pycache__/__init__.cpython-312.pyc,,
weasyprint/css/validation/__pycache__/descriptors.cpython-312.pyc,,
weasyprint/css/validation/__pycache__/expanders.cpython-312.pyc,,
weasyprint/css/validation/__pycache__/properties.cpython-312.pyc,,
weasyprint/css/validation/descriptors.py,sha256=VOLmaKZP76mTVwMCqMzVgfzP1JaI4zYWdEIFLNXCfYQ,11160
weasyprint/css/validation/expanders.py,sha256=9SJiVhoK8HESwwmN2IqXj-bmaC9p4SSD15tqmfMITnE,39263
weasyprint/css/validation/properties.py,sha256=1xoYf0rEs4dKZDeZT7Qh-Z3_GmrBJ1pQ8ZozEIJOgyE,66811
weasyprint/document.py,sha256=pUwJKcUp3tmgxh155RsQpS3kVUMuXWZfXCOziqQwaNc,17131
weasyprint/draw/__init__.py,sha256=JGk_UCt045atrZPkLlUDNNlqeV7ngze487-v_wqwG0w,22914
weasyprint/draw/__pycache__/__init__.cpython-312.pyc,,
weasyprint/draw/__pycache__/border.cpython-312.pyc,,
weasyprint/draw/__pycache__/color.cpython-312.pyc,,
weasyprint/draw/__pycache__/stack.cpython-312.pyc,,
weasyprint/draw/__pycache__/text.cpython-312.pyc,,
weasyprint/draw/border.py,sha256=MRG9E7Bi_QLGkSVEgEJ7D1QlFbs3atkATAAA2s9a6wY,27910
weasyprint/draw/color.py,sha256=ZjqiMDSNIFOLsIz2qP30HrFMbVqeEmYAKxcU6tl4GBs,1449
weasyprint/draw/stack.py,sha256=o9VB8GtAdLfbrzQpZ1SWiV_hneHAz64egURawBLYbMo,281
weasyprint/draw/text.py,sha256=L_5VqeAzz2PpPCdBhl7Dfza08NteNU0SfbAwITEaroE,11419
weasyprint/formatting_structure/__pycache__/boxes.cpython-312.pyc,,
weasyprint/formatting_structure/__pycache__/build.cpython-312.pyc,,
weasyprint/formatting_structure/boxes.py,sha256=U3HPSaM9mbzQSbZCo_ZQp8VAS48KVOsQaH0g08iBGpc,26279
weasyprint/formatting_structure/build.py,sha256=c86cB4K8JL2oVMogFOWQn4V_GIhN1yEQL0z_wMqgPjo,56697
weasyprint/html.py,sha256=om7dvhx12ecunTaVclmWejkr5nPQF1BZmeH7jqQ_4GM,11590
weasyprint/images.py,sha256=PI6xXN8z7vB79wK4RB9x3bLDwpPw7dQHZlzV4mLzqjM,35555
weasyprint/layout/__init__.py,sha256=TNzbltiiuLVLLOlFDTLbwVlOoS7OnOzNPmHtWeaLk3M,16292
weasyprint/layout/__pycache__/__init__.cpython-312.pyc,,
weasyprint/layout/__pycache__/absolute.cpython-312.pyc,,
weasyprint/layout/__pycache__/background.cpython-312.pyc,,
weasyprint/layout/__pycache__/block.cpython-312.pyc,,
weasyprint/layout/__pycache__/column.cpython-312.pyc,,
weasyprint/layout/__pycache__/flex.cpython-312.pyc,,
weasyprint/layout/__pycache__/float.cpython-312.pyc,,
weasyprint/layout/__pycache__/grid.cpython-312.pyc,,
weasyprint/layout/__pycache__/inline.cpython-312.pyc,,
weasyprint/layout/__pycache__/leader.cpython-312.pyc,,
weasyprint/layout/__pycache__/min_max.cpython-312.pyc,,
weasyprint/layout/__pycache__/page.cpython-312.pyc,,
weasyprint/layout/__pycache__/percent.cpython-312.pyc,,
weasyprint/layout/__pycache__/preferred.cpython-312.pyc,,
weasyprint/layout/__pycache__/replaced.cpython-312.pyc,,
weasyprint/layout/__pycache__/table.cpython-312.pyc,,
weasyprint/layout/absolute.py,sha256=Caek_aFJDz5NLYFxTfEYO1-l7ITUMcx6TNDjNpE15-Y,13981
weasyprint/layout/background.py,sha256=0ZRFZGAJnvF9eULIMyt8oy6lDqE-hKSbcyomQPx8HsQ,10008
weasyprint/layout/block.py,sha256=jwQS2jGCoQ0hSvCsWRPFK7Y3wad-Ucgzv0fzf-4DKm0,45388
weasyprint/layout/column.py,sha256=P_863u0_vFYzig9tfBUMI8ZCpXlbx9SLoYekbZ5vg5Y,17299
weasyprint/layout/flex.py,sha256=uAm4DAOvomSg0aX1oEse8-vT3M_fVI-IGFO1yatBH6E,42630
weasyprint/layout/float.py,sha256=xo9CuXfvrC5jE8rnCVT2ShNRAXl7vAISq41oqkvvbK8,9266
weasyprint/layout/grid.py,sha256=hzmsWXWQYw5rdy2XA_O_4Hz7CWCVle0RnnWTkugPEvY,54729
weasyprint/layout/inline.py,sha256=aFxHM0b-fQCSIWyXgf3dLHM90sQq8AOcS1ELx7sDpbc,48217
weasyprint/layout/leader.py,sha256=wklI0aLyTx0VJhqU7D_FxtJpfe7dXswcN-VApAusM-Q,2825
weasyprint/layout/min_max.py,sha256=JdXJG9ISO_RsfeHua_-3g477a16I-NrnYuwH_tQwq4o,1527
weasyprint/layout/page.py,sha256=JLKoYzGmrULONv11KLLrQuRFaTRK7XJZocuUsB0EGKs,39908
weasyprint/layout/percent.py,sha256=2XzT_Y-fu7OVt2Ut1f9T1Tt9S4ftRr4x7wL3agvEJus,5626
weasyprint/layout/preferred.py,sha256=cSOraU8g7i_8dDUifJhvKk0gxHBdPXqGZjjIgs8LVIs,31040
weasyprint/layout/replaced.py,sha256=ucAd6VMKIEryjnwK8ciKbUoE2yK29-ggdYlGW3KPDXk,11178
weasyprint/layout/table.py,sha256=qomzhxW0m4WaZ4Vis4GW7bfNQ6JP7yRSt7aze-dNcbU,47431
weasyprint/logger.py,sha256=z1q548fX5shfAyLoMLeM9ozWGKgoBTKQsdlTtfRE_9U,1824
weasyprint/matrix.py,sha256=v1BPtyn_-S_4TrAUgzOOR-viUXgdqsABKRndCEprkPc,1909
weasyprint/pdf/__init__.py,sha256=pEehJ-JDH0ZepUFtj3Fz75BvVGBf7psduIbnph08PRo,12022
weasyprint/pdf/__pycache__/__init__.cpython-312.pyc,,
weasyprint/pdf/__pycache__/anchors.cpython-312.pyc,,
weasyprint/pdf/__pycache__/debug.cpython-312.pyc,,
weasyprint/pdf/__pycache__/fonts.cpython-312.pyc,,
weasyprint/pdf/__pycache__/metadata.cpython-312.pyc,,
weasyprint/pdf/__pycache__/pdfa.cpython-312.pyc,,
weasyprint/pdf/__pycache__/pdfua.cpython-312.pyc,,
weasyprint/pdf/__pycache__/stream.cpython-312.pyc,,
weasyprint/pdf/anchors.py,sha256=Slgq3_IBiIBg3J7zzHMajISBb7ph2AVezIs-lW_tB9I,17386
weasyprint/pdf/debug.py,sha256=reLw6U6hK94FOVNYW8psdt_SFN11iIe1rhYkr6sURF4,1407
weasyprint/pdf/fonts.py,sha256=D_T0JWFnofSDqdU29noTg8IbyenN9SlaULxSTPlg1JQ,24777
weasyprint/pdf/metadata.py,sha256=r5ATj8Lv_6Ib-RbA2zgazyo6yJwF-LoqoNAWUchV4qE,4168
weasyprint/pdf/pdfa.py,sha256=97J7nlKHmP5vdSBz0X0cDnGnqAPZ5qqoQ7ArZsdWRT8,3626
weasyprint/pdf/pdfua.py,sha256=mCgXdr1RUe6hffg6Jsu4LzmDtBNyJTtj32wTouBeJy4,5236
weasyprint/pdf/sRGB2014.icc,sha256=OEuDLeNBIGZ0O1KnXukGtvufuNngnpNvwsQyI4Fcbgo,3024
weasyprint/pdf/stream.py,sha256=-s_ZT2xDsKzhjj9cgMnf4OkvVp88EnKBwzAvxKwOwWs,11301
weasyprint/stacking.py,sha256=oFB1-UHHeY8jO9nnS-IU5KK55azQ7V5QjEHYWGSqGW0,5628
weasyprint/svg/__init__.py,sha256=smY5L9f68vf3BTZL1KTB0fz5JcwFM0kxFgywgRAOxdE,30012
weasyprint/svg/__pycache__/__init__.cpython-312.pyc,,
weasyprint/svg/__pycache__/bounding_box.cpython-312.pyc,,
weasyprint/svg/__pycache__/css.cpython-312.pyc,,
weasyprint/svg/__pycache__/defs.cpython-312.pyc,,
weasyprint/svg/__pycache__/images.cpython-312.pyc,,
weasyprint/svg/__pycache__/path.cpython-312.pyc,,
weasyprint/svg/__pycache__/shapes.cpython-312.pyc,,
weasyprint/svg/__pycache__/text.cpython-312.pyc,,
weasyprint/svg/__pycache__/utils.cpython-312.pyc,,
weasyprint/svg/bounding_box.py,sha256=ti9BZ8Pxr4K_RZr41vrcDeBu1Mz9CReh8ECbUzzh_0s,12999
weasyprint/svg/css.py,sha256=AUsIim2rOmRGLgFuiWm4EzXwnrRlThczfM17Uq2MRUg,3832
weasyprint/svg/defs.py,sha256=u_VQ-b6045qCBuLNEe9lwJWHcOsYYO4wBwsSWiXEZm0,20978
weasyprint/svg/images.py,sha256=3A3pulL4cDPQ22uz0QqyQ78qcRIp_sEyAAIxjyj62d0,3059
weasyprint/svg/path.py,sha256=Z-T6kbUU3pyHhzVV0JSBgO--XaCGXLsH-cS9iAsITMM,10064
weasyprint/svg/shapes.py,sha256=NDo0KMnwrm0hj3BOmfrKjRZo4iJF9o-MeUhZ5avANco,3845
weasyprint/svg/text.py,sha256=JVrLSpDtU3P9IgkG50g_lQVi0L5uNbXoEDh6tk3o2z4,6404
weasyprint/svg/utils.py,sha256=RkmPhTAqBfn85YACP3u9m4Rs8hyqZyQLBLME1q8psyw,6969
weasyprint/text/__pycache__/constants.cpython-312.pyc,,
weasyprint/text/__pycache__/ffi.cpython-312.pyc,,
weasyprint/text/__pycache__/fonts.cpython-312.pyc,,
weasyprint/text/__pycache__/line_break.cpython-312.pyc,,
weasyprint/text/constants.py,sha256=ifPeTG_us_sSgWuM-WTQgDrrAgwwnohYR63HhS_1dIM,14191
weasyprint/text/ffi.py,sha256=0FWxNeYn0Nub-fKHWElFcQc9GmlgiLAKvS82hpDxsAs,18282
weasyprint/text/fonts.py,sha256=Qa7HxrQeJN84XXBKgJitcRdVMRA9vz1QY8Kl3StRCE4,17460
weasyprint/text/line_break.py,sha256=FXlHDk8A4lBB-i3wqgl7RO66P3TPpFzkkFbmu-FHGKg,24735
weasyprint/urls.py,sha256=yf6pXTic73l_nZiYkX03bfgFl2HdQlH0rCs4bSY5tjQ,9960
