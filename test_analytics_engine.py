"""
Test script for SPX Analytics Engine
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))

from config import Config
from data_loader import SPXDataLoader
from analytics_engine import GreeksAnalyticsEngine

def test_analytics_engine():
    """Test the analytics engine functionality"""
    print("Testing SPX Analytics Engine...")
    print("=" * 50)
    
    # Initialize and load data
    config = Config()
    config.ensure_directories()
    
    loader = SPXDataLoader(config)
    loader.load_data()
    loader.clean_data()
    loader.calculate_derived_greeks()
    daily_data = loader.aggregate_daily_data()
    
    print(f"Loaded {len(daily_data)} days of data for analysis")
    
    # Initialize analytics engine
    engine = GreeksAnalyticsEngine(config)
    engine.set_data(daily_data)
    
    # Test convergence events identification
    print("\n1. Testing convergence events identification...")
    try:
        convergence_events = engine.identify_convergence_events()
        print(f"   ✓ Found {len(convergence_events)} convergence events")
        
        if len(convergence_events) > 0:
            print("   Sample convergence events:")
            sample_cols = ['date', 'spx_close', 'Vomma_M', 'Vanna_K', 'Total_OI_K', 'extreme_score', 'event_type']
            sample = convergence_events[sample_cols].head(3)
            print(sample.to_string(index=False))
    except Exception as e:
        print(f"   ✗ Error in convergence analysis: {e}")
        return False
    
    # Test Vomma/Price divergence analysis
    print("\n2. Testing Vomma/Price divergence analysis...")
    try:
        divergence_analysis = engine.analyze_vomma_price_divergence()
        print(f"   ✓ Distribution signals: {len(divergence_analysis['distribution_signals'])}")
        print(f"   ✓ Acceleration signals: {len(divergence_analysis['acceleration_signals'])}")
        print(f"   ✓ Vomma-Price correlation: {divergence_analysis['correlation_vomma_price']:.3f}")
        
        if len(divergence_analysis['distribution_signals']) > 0:
            print("   Sample distribution signals:")
            sample_cols = ['date', 'spx_close', 'price_change', 'Vomma_M', 'vomma_change']
            sample = divergence_analysis['distribution_signals'][sample_cols].head(3)
            print(sample.to_string(index=False))
    except Exception as e:
        print(f"   ✗ Error in divergence analysis: {e}")
        return False
    
    # Test Theta decay analysis
    print("\n3. Testing Theta decay analysis...")
    try:
        theta_analysis = engine.analyze_theta_decay_urgency()
        print(f"   ✓ Theta collapse events: {len(theta_analysis['theta_collapse_events'])}")
        print(f"   ✓ Dangerous combinations: {len(theta_analysis['dangerous_combinations'])}")
        print(f"   ✓ Theta statistics: mean={theta_analysis['theta_statistics']['mean']:.2f}M")
    except Exception as e:
        print(f"   ✗ Error in theta analysis: {e}")
        return False
    
    # Test volume pattern analysis
    print("\n4. Testing volume pattern analysis...")
    try:
        volume_analysis = engine.analyze_volume_patterns()
        print(f"   ✓ Volume collapse events: {len(volume_analysis['volume_collapse_events'])}")
        print(f"   ✓ Volume spike events: {len(volume_analysis['volume_spike_events'])}")
        print(f"   ✓ Average volume: {volume_analysis['volume_statistics']['mean']:.0f}")
    except Exception as e:
        print(f"   ✗ Error in volume analysis: {e}")
        return False
    
    # Test Greek correlations
    print("\n5. Testing Greek correlations...")
    try:
        correlations = engine.calculate_greek_correlations()
        print(f"   ✓ Calculated correlation matrix")
        print(f"   ✓ Key correlations:")
        for key, value in correlations['key_correlations'].items():
            print(f"     {key}: {value:.3f}")
    except Exception as e:
        print(f"   ✗ Error in correlation analysis: {e}")
        return False
    
    # Test market regime identification
    print("\n6. Testing market regime identification...")
    try:
        regimes = engine.identify_market_regimes()
        print(f"   ✓ Volatility regimes: {regimes['volatility_regimes']}")
        print(f"   ✓ Greek regimes: {regimes['greek_regimes']}")
    except Exception as e:
        print(f"   ✗ Error in regime analysis: {e}")
        return False
    
    # Test full analysis pipeline
    print("\n7. Testing full analysis pipeline...")
    try:
        full_results = engine.run_full_analysis()
        print(f"   ✓ Full analysis completed")
        print(f"   ✓ Summary:")
        summary = full_results['summary']
        for key, value in summary['key_findings'].items():
            print(f"     {key}: {value}")
    except Exception as e:
        print(f"   ✗ Error in full analysis: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✓ All analytics engine tests passed!")
    return True

if __name__ == "__main__":
    success = test_analytics_engine()
    if not success:
        sys.exit(1)

